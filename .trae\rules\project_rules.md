# Frontend Projesi <PERSON> (project_rules.md)

<PERSON><PERSON>, projenin frontend geliştirme süreçlerinde uyulması gereken standartları, en iyi pratikleri ve kuralları içerir. Ana `user_rules.md` dosyasındaki tüm kurallar geçerlidir.

---

## 0. <PERSON><PERSON>ımı ve Ana Terminoloji

### Projenin Amacı

Bu proje, bir giyim mağazasının ticari operasyonlarını yönetmek için geliştirilmiş bir web uygulamasıdır. Temel amacı; müşteri kayıtlarını tutmak, yapılan satışları ve bu satışlara bağlı taksitleri yönetmek, ödemeleri takip etmek ve isteğe bağlı olarak ürün ve stok yönetimini sağlamaktır.

### Ana Varlıklar ve Terminoloji

Geliştirme sırasında tutarlılığı sağlamak için aşağıdaki terimler kullanılacaktır:

* **Müşteri (Customer):** Mağazadan alışveriş yapan, borç ve ödeme geçmişi olan kişi veya kurum.
* **Satış (Sale):** Bir veya daha fazla ürünün bir müşteriye satılması işlemi. Her satışın bir toplam tutarı vardır.
* **Taksit (Installment):** Bir satışın toplam tutarının bölündüğü ödeme planı parçalarından her biri. Her taksitin bir vadesi ve tutarı bulunur.
* **Ödeme (Payment):** Bir taksite veya doğrudan bir satışa karşılık yapılan para girişi.
* **Ürün (Product):** Mağazada satılan giyim eşyaları ve diğer kalemler.
* **User (Kullanıcı):** Mağazaya erişim sağlayan ve işlemler yapabilen kişi.
  
---

### 1. Genel Prensipler ve Mimarî

1.  **Mevcut Yapıya Uyum:** En önemli kural, projenin mevcut dizin yapısına, isimlendirme standartlarına ve mimari desenlerine (örneğin, state yönetimi, servis katmanı yapısı vb.) tam uyum sağlamaktır. Yeni bir özellik geliştirilirken önce mevcut benzer yapılar incelenmelidir.
2.  **Tek Sorumluluk Prensibi (Single Responsibility):** Her Vue bileşeni, composable fonksiyon veya servis, iyi tanımlanmış tek bir sorumluluğa sahip olmalıdır.

---

### 2. Performans ve Optimizasyon

1.  **Lazy Loading (Tembel Yükleme):** Vue Router'da, `views` (sayfalar) her zaman dinamik `import()` ile tembel yüklenmelidir. Bu, ilk yükleme süresini kısaltır.
2.  **Gereksiz Yeniden Çizimden Kaçınma:** Hesaplama maliyeti yüksek işlemler için `computed` özelliklerini kullan. `ref` ve `reactive`'i doğru durumlarda kullanarak gereksiz reaktiviteden kaçın.
3.  **Bundle Boyutu:** Projeye yeni bir bağımlılık eklemeden önce, getireceği ek yükü göz önünde bulundur. Gerekli durumlarda `vite-plugin-bundle-analyzer` gibi araçlarla paket boyutunu analiz et.
