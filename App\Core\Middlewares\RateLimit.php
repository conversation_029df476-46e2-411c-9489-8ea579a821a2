<?php

declare(strict_types=1);

namespace App\Core\Middlewares;

use System\Http\Request;
use System\Exception\SystemException;

class RateLimit {
    private int $maxAttempts = 60; // dakikada 60 istek
    private int $decayMinutes = 1;

    public function __construct(private Request $request) {}

    public function handle(callable $next): mixed {
        $key = $this->resolveRequestSignature();
        $attempts = $this->getAttempts($key);

        if ($attempts >= $this->maxAttempts) {
            throw new SystemException('Too many requests', 429);
        }

        $this->incrementAttempts($key);
        return $next();
    }

    private function resolveRequestSignature(): string {
        return sha1($this->request->ip() . '|' . $this->request->uri());
    }

    private function getAttempts(string $key): int {
        // Cache'den attempt sayısını al
        return (int) ($_SESSION['rate_limit'][$key] ?? 0);
    }

    private function incrementAttempts(string $key): void {
        $_SESSION['rate_limit'][$key] = $this->getAttempts($key) + 1;
    }
}