<?php

declare(strict_types=1);

use System\Migration\Migration;

class customer extends Migration {
   public function up() {
      $this->database->query("CREATE TABLE IF NOT EXISTS customer (
         `id` INT AUTO_INCREMENT PRIMARY KEY,
         `name` VA<PERSON>HAR(50) NOT NULL,
         `surname` VARCHAR(50) NOT NULL,
         `email` VARCHAR(150) DEFAULT NULL,
         `phone` VARCHAR(50) UNIQUE NOT NULL,
         `tckn` VARCHAR(50) DEFAULT NULL,
         `address` VARCHAR(50) DEFAULT NULL,
         `current_debit` DECIMAL(10,2) NOT NULL DEFAULT 0,
         `is_risky` INT NOT NULL DEFAULT 0,
         `is_active` INT NOT NULL DEFAULT 1,
         `notes` VARCHAR(255) NOT NULL DEFAULT '',
         {$this->defaults()}
      )");

      $this->database->table('customer')->insert([
         'name' => 'Ah<PERSON>',
         'surname' => '<PERSON><PERSON><PERSON><PERSON>',
         'email' => '<EMAIL>',
         'phone' => '05551234567',
         'tckn' => '12345678901',
         'address' => 'İstanbul',
         'current_debit' => 0,
         'is_risky' => 0,
         'is_active' => 1,
         'notes' => 'Test müşterisi'
      ])->prepare()->execute();

      $this->database->table('customer')->insert([
         'name' => 'Fatma',
         'surname' => 'Demir',
         'email' => '<EMAIL>',
         'phone' => '05559876543',
         'tckn' => '98765432109',
         'address' => 'Ankara',
         'current_debit' => 150.50,
         'is_risky' => 0,
         'is_active' => 1,
         'notes' => 'VIP müşteri'
      ])->prepare()->execute();
   }

   public function down() {
      $this->database->query("DROP TABLE IF EXISTS customer");
   }
}
