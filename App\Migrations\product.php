<?php

declare(strict_types=1);

use System\Migration\Migration;

class product extends Migration {
   public function up() {
      $this->database->query("CREATE TABLE IF NOT EXISTS `product` (
         `id` INT AUTO_INCREMENT PRIMARY KEY,
         `code` VARCHAR(50) NOT NULL,
         `title` VARCHAR(250) NOT NULL,
         `price` DECIMAL(10,2) NOT NULL,
         `content` TEXT NULL DEFAULT NULL,
         `stock` INT NOT NULL DEFAULT 0,
         `category_id` INT NULL DEFAULT NULL,
         `image_path` VARCHAR(250) NULL DEFAULT NULL,
         `is_active` BOOLEAN NOT NULL DEFAULT 1,
         `sort_order` INT NOT NULL DEFAULT 0,
         `is_temp` BOOLEAN NOT NULL DEFAULT 0,
         {$this->defaults()}
      )");

      $this->database->table('product')->insert([
         'code' => 'PRD001',
         'title' => '<PERSON><PERSON><PERSON><PERSON>',
         'content' => '<PERSON>r<PERSON><PERSON>',
         'price' => 100,
         'stock' => 3,
         'category_id' => 1,
         'is_active' => 1,
         'sort_order' => 1,
         'is_temp' => 0
      ])->prepare()->execute();

      $this->database->table('product')->insert([
         'code' => 'PRD002',
         'title' => 'Ürün Başlığı',
         'content' => 'Ürün Açıklaması',
         'price' => 200,
         'stock' => 5,
         'category_id' => 1,
         'is_active' => 1,
         'sort_order' => 1,
         'is_temp' => 0,
      ])->prepare()->execute();
   }

   public function down() {
      $this->database->query("DROP TABLE IF EXISTS `product`");
   }
}
