<?php

declare(strict_types=1);

namespace App\Modules\Customer;

use System\Database\Database;
use App\Core\Abstracts\BaseRepository;

class CustomerRepository extends BaseRepository {
   public function __construct(
      protected Database $database,
      protected string $table = 'customer'
   ) {
   }
   public function getSales(int $customer_id): array {
      return $this->database
         ->prepare('SELECT *
            FROM sale
            WHERE sale.deleted_at IS NULL
               AND sale.customer_id = :customer_id
         ')
         ->execute([
            'customer_id' => $customer_id,
         ])
         ->fetchAll();
   }
   public function getInstallments(int $customer_id): array {
      return $this->database
         ->prepare('SELECT *
            FROM installment
            JOIN sale ON sale.id = installment.sale_id
               AND sale.deleted_at IS NULL
            WHERE installment.deleted_at IS NULL
               AND sale.customer_id = :customer_id
         ')
         ->execute([
            'customer_id' => $customer_id,
         ])
         ->fetchAll();
   }
}
