<?php

declare(strict_types=1);

namespace App\Modules\Customer;

use System\Database\Database;
use System\Validation\Validation;
use App\Core\Abstracts\BaseService;
use System\Exception\SystemException;
use App\Modules\Customer\CustomerRequest;
use App\Modules\Customer\CustomerRepository;

class CustomerService extends BaseService {
   /** @var CustomerRepository */
   protected mixed $repository;

   public function __construct(
      protected Database $database,
      protected Validation $validation,
      CustomerRepository $repository,
   ) {
      $this->repository = $repository;
   }
   public function getCustomer(int $id): array {
      $result = $this->repository->findOne($id);
      if (empty($result)) {
         throw new SystemException('Record not found', 404);
      }

      return $result;
   }

   public function createCustomer(CustomerRequest $dto): array {
      $existingPhone = $this->repository->findBy(['phone' => $dto->phone]);
      $existingTckn = $this->repository->findBy(['tckn' => $dto->tckn]);
      $existingEmail = $this->repository->findBy(['email' => $dto->email]);

      if (!empty($existingPhone)) {
         throw new SystemException('Phone already exists', 400);
      }
      if (!empty($existingTckn)) {
         throw new SystemException('TCKN already exists', 400);
      }
      if (!empty($existingEmail)) {
         throw new SystemException('Email already exists', 400);
      }
      return $this->transaction(function () use ($dto) {
         $this->validate($dto->toArray(), [
            'name' => 'required',
            'surname' => 'required',
            'phone' => 'required'
         ]);

         $id = $this->create([
            'name' => $dto->name,
            'surname' => $dto->surname,
            'email' => $dto->email,
            'phone' => $dto->phone,
            'tckn' => $dto->tckn,
            'address' => $dto->address,
            'current_debit' => $dto->current_debit,
            'is_risky' => $dto->is_risky,
            'is_active' => $dto->is_active,
            'notes' => $dto->notes
         ]);

         return $this->getCustomer($id);
      });
   }

   public function updateCustomer(CustomerRequest $dto): array {
      return $this->transaction(function () use ($dto) {
         $this->check([
            'id' => $dto->id
         ]);

         $this->validate($dto->toArray(), [
            'name' => 'required',
            'surname' => 'required',
            'phone' => 'required'
         ]);

         $this->update($dto, [
            'name' => $dto->name,
            'surname' => $dto->surname,
            'email' => $dto->email,
            'phone' => $dto->phone,
            'tckn' => $dto->tckn,
            'address' => $dto->address,
            'current_debit' => $dto->current_debit,
            'is_risky' => $dto->is_risky,
            'is_active' => $dto->is_active,
            'notes' => $dto->notes
         ], [
            'id' => $dto->id
         ]);

         return $this->getCustomer($dto->id);
      });
   }

   public function getSales(int $customer_id): array {
      return $this->repository->getSales($customer_id);
   }
   public function getInstallments(int $customer_id): array {
      return $this->repository->getInstallments($customer_id);
   }
}
