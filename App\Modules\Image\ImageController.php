<?php

declare(strict_types=1);

namespace App\Modules\Image;

use System\Http\Request;
use System\Http\Response;
use App\Modules\Image\ImageService;
use App\Core\Abstracts\BaseController;

class ImageController extends BaseController {
   public function __construct(
      protected Response $response,
      protected Request $request,
      protected ImageService $service
   ) {
   }

   public function uploadImage() {
      $this->response(function () {
         $files = array_values($this->request->files());
         $path = $this->request->post('path');
         $result = $this->service->upload($files, $path);
         return $result;
      }, code: 201);
   }

   public function deleteImage() {
      $this->response(function () {
         $request = $this->request->json();
         $result = $this->service->unlink($request);
         return $result;
      });
   }
}
