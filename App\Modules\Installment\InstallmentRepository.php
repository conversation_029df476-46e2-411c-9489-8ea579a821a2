<?php

declare(strict_types=1);

namespace App\Modules\Installment;

use System\Database\Database;
use App\Core\Abstracts\BaseRepository;

class InstallmentRepository extends BaseRepository {
   public function __construct(
      protected Database $database,
      protected string $table = 'installment'
   ) {
   }

   public function getInstallmentBySale(int $installment_id): array {
      return $this->database
         ->prepare('SELECT *
            FROM installment
            JOIN sale ON sale.id = installment.sale_id
               AND sale.deleted_at IS NULL
            WHERE installment.deleted_at IS NULL
               AND installment.id = :installment_id
         ')
         ->execute([
            'installment_id' => $installment_id,
         ])
         ->fetch();
   }
   public function getInstallmentByCustomer(int $installment_id): array {
      return $this->database
         ->prepare('SELECT *
            FROM installment
            JOIN sale ON sale.id = installment.sale_id
               AND sale.deleted_at IS NULL
            JOIN customer ON customer.id = sale.customer_id
               AND customer.deleted_at IS NULL
            WHERE installment.deleted_at IS NULL
               AND installment.id = :installment_id
         ')
         ->execute([
            'installment_id' => $installment_id,
         ])
         ->fetch();
   }
}
