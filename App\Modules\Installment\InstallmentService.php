<?php

declare(strict_types=1);

namespace App\Modules\Installment;

use System\Database\Database;
use System\Validation\Validation;
use App\Core\Abstracts\BaseService;
use System\Exception\SystemException;
use App\Modules\Installment\InstallmentRepository;

class InstallmentService extends BaseService {
   /** @var InstallmentRepository */
   protected mixed $repository;

   public function __construct(
      protected Database $database,
      protected Validation $validation,
      InstallmentRepository $repository,
   ) {
      $this->repository = $repository;
   }

   public function getAllInstallment(): array {
      $result = $this->repository->findAll();
      return $result;
   }

   public function getInstallment(int $id): array {
      $result = $this->repository->findOne($id);
      if (empty($result)) {
         throw new SystemException('Record not found', 404);
      }

      return $result;
   }

   public function createInstallment(InstallmentRequest $dto): array {
      $sale = $this->repository->findBy(['id' => $dto->sale_id], 'sale');
      if (empty($sale)) {
         throw new SystemException('Sale not found', 404);
      }
      if(!in_array($dto->status, ['pending', 'paid', 'overdue'])) {
         throw new SystemException('Invalid status', 400);
      }
      return $this->transaction(function () use ($dto) {
         $this->validate($dto->toArray(), [
            'sale_id' => 'required|numeric',
            'amount' => 'required|numeric',
            'payment' => 'required|numeric',
            'status' => 'required',
            'due_at' => 'required'
         ]);

         $id = $this->create([
            'sale_id' => $dto->sale_id,
            'amount' => $dto->amount,
            'payment' => $dto->payment,
            'status' => $dto->status,
            'due_at' => $dto->due_at,
            'paid_at' => empty($dto->paid_at) ? null : $dto->paid_at
         ]);

         return $this->getInstallment($id);
      });
   }

   public function updateInstallment(InstallmentRequest $dto): array {
      return $this->transaction(function () use ($dto) {
         $this->check([
            'id' => $dto->id
         ]);

         $this->validate($dto->toArray(), [
            'amount' => 'required|numeric',
            'payment' => 'required|numeric',
            'status' => 'required',
            'due_at' => 'required'
         ]);

         $this->update($dto, [
            'sale_id' => $dto->sale_id,
            'amount' => $dto->amount,
            'payment' => $dto->payment,
            'status' => $dto->status,
            'due_at' => $dto->due_at,
            'paid_at' => empty($dto->paid_at) ? null : $dto->paid_at
         ],
         [
            'id' => $dto->id
         ]);

         return $this->getInstallment($dto->id);
      });
   }

}
