<?php

declare(strict_types=1);

namespace App\Modules\Product;

use System\Http\Request;
use System\Http\Response;
use App\Core\Abstracts\BaseController;
use App\Modules\Product\ProductRequest;
use App\Modules\Product\ProductService;

/**
 * @OA\Tag(name="Product", description="Ürün işlemleri")
 */
class ProductController extends BaseController {
   public function __construct(
      protected Response $response,
      protected Request $request,
      protected ProductService $service
   ) {
   }

   /**
    * @OA\Get(
    *    tags={"Product"}, path="/product/", summary="Ürün listesi",
    *    @OA\Response(response=200, description="Success")
    * )
    */
   public function getAllProduct() {
      $this->response(function () {
         $result = $this->service->getAll();
         return array_map(function ($item) {
            $installment = new ProductResponse();
            $installment->fromArray($item);
            return $installment;
         }, $result);
      });
   }

   /**
    * @OA\Get(tags={"Product"}, path="/product/{id}", summary="Ürün detayı (ID'ye göre)",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer"))
    * )
    */
   public function getProduct(int $id) {
      $this->response(function () use ($id) {
         $result = $this->service->getOne($id);
         $installment = new ProductResponse();
         $installment->fromArray($result);
         return $installment;
      });
   }

   /**
    * @OA\Post(tags={"Product"}, path="/product/", summary="Ürün ekle",
    *    @OA\Response(response=201, description="Success"),
    *    @OA\RequestBody(required=true, @OA\JsonContent(
    *       required={"title", "price", "is_active", "sort_order"},
    *       @OA\Property(property="code", type="string", example="PRD001"),
    *       @OA\Property(property="title", type="string", example="Koruyucu Eldiven"),
    *       @OA\Property(property="content", type="string", example="Yüksek kaliteli koruyucu eldiven"),
    *       @OA\Property(property="price", type="number", example=100),
    *       @OA\Property(property="stock", type="integer", example=10),
    *       @OA\Property(property="category_id", type="integer", example=1),
    *       @OA\Property(property="is_active", type="integer", example=1),
    *       @OA\Property(property="sort_order", type="integer", example=1),
    *       @OA\Property(property="is_temp", type="integer", example=0)
    *    ))
    * )
    */
   public function createProduct() {
      $this->response(function () {
         $request = $this->request->json();
         $dto = new ProductRequest();
         $dto->fromArray($request);
         $result = $this->service->createProduct($dto);
         return $result;
      }, code: 201);
   }

   /**
    * @OA\Put(tags={"Product"}, path="/product/", summary="Ürün güncelle",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\RequestBody(required=true, @OA\JsonContent(
    *       required={"id", "title", "price", "is_active", "sort_order"},
    *       @OA\Property(property="id", type="integer", example=1),
    *       @OA\Property(property="code", type="string", example="PRD001"),
    *       @OA\Property(property="title", type="string", example="Koruyucu Eldiven"),
    *       @OA\Property(property="content", type="string", example="Yüksek kaliteli koruyucu eldiven"),
    *       @OA\Property(property="price", type="number", example=100),
    *       @OA\Property(property="stock", type="integer", example=10),
    *       @OA\Property(property="category_id", type="integer", example=1),
    *       @OA\Property(property="is_active", type="integer", example=1),
    *       @OA\Property(property="sort_order", type="integer", example=1),
    *       @OA\Property(property="is_temp", type="integer", example=0)
    *    ))
    * )
    */
   public function updateProduct() {
      $this->response(function () {
         $request = $this->request->json();
         $dto = new ProductRequest();
         $dto->fromArray($request);
         $result = $this->service->updateProduct($dto);
         return $result;
      });
   }

   /**
    * @OA\Delete(
    *    tags={"Product"}, path="/product/{id}", summary="Ürün sil",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer"))
    * )
    */
   public function deleteProduct(int $id) {
      $this->response(function () use ($id) {
         $result = $this->service->delete(
            ['id' => $id,
             'deleted_at' => ['IS NULL']
            ]);
         return $result;
      });
   }
}
