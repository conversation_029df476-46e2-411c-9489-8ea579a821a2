<?php

declare(strict_types=1);

namespace App\Modules\Product;

use System\Database\Database;
use App\Core\Abstracts\BaseRepository;

class ProductRepository extends BaseRepository {
   public function __construct(
      protected Database $database,
      protected string $table = 'product'
   ) {
   }

   public function findCategory(int $id): array|false {
      return $this->database
         ->prepare('SELECT
               id,
               title
            FROM category
            WHERE id = :id
         ')
         ->execute([
            'id' => $id,
         ])
         ->fetch();
   }
}
