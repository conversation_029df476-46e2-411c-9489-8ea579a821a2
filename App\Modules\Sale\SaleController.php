<?php

declare(strict_types=1);

namespace App\Modules\Sale;

use System\Http\Request;
use System\Http\Response;
use App\Core\Abstracts\BaseController;

/**
 * @OA\Tag(name="Sale", description="Satış işlemleri")
 */
class SaleController extends BaseController {
   public function __construct(
      protected Response $response,
      protected Request $request,
      protected SaleService $service
   ) {
   }

   /**
    * @OA\Get(
    *    tags={"Sale"}, path="/sale/", summary="Satış listesi",
    *    @OA\Response(response=200, description="Success")
    * )
    */
   public function getAllSale() {
      $this->response(function () {
         $response = $this->service->getAll();
         return array_map(function ($item) {
            $sale = new SaleResponse();
            $sale->fromArray($item);
            return $sale;
         }, $response);
      });
   }

   /**
    * @OA\Get(tags={"Sale"}, path="/sale/{id}", summary="Satış detayı (ID'ye göre)",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer"))
    * )
    */
   public function getSale(int $id) {
      $this->response(function () use ($id) {
         $result = $this->service->getSale($id);
         return $result;
      });
   }

   /**
    * @OA\Post(tags={"Sale"}, path="/sale/", summary="Satış ekle",
    *    @OA\Response(response=201, description="Success"),
    *    @OA\RequestBody(required=true, @OA\JsonContent(
    *    required={"customer_id", "product", "price", "installment", "installment_type"},
    *    @OA\Property(property="customer_id", type="integer", example=1),
    *    @OA\Property(property="product", type="string", example="Koruyucu Eldiven"),
    *    @OA\Property(property="price", type="number", example=100),
    *    @OA\Property(property="installment", type="integer", example=1),
    *    @OA\Property(property="installment_type", type="string", example="monthly"),
    *    @OA\Property(property="notes", type="string", example="Notlar")
    *    ))
    * )
    */
   public function createSale() {
      $this->response(function () {
         $request = $this->request->json();
         $dto = new SaleRequest();
         $dto->fromArray($request);
         $result = $this->service->createSale($dto);
         return $result;
      }, code: 201);
   }

   /**
    * @OA\Put(tags={"Sale"}, path="/sale/", summary="Satış güncelle",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\RequestBody(required=true, @OA\JsonContent(
    *    required={"id", "customer_id", "product", "price", "installment", "installment_type"},
    *    @OA\Property(property="id", type="integer", example=1),
    *    @OA\Property(property="customer_id", type="integer", example=1),
    *    @OA\Property(property="product", type="string", example="Koruyucu Eldiven"),
    *    @OA\Property(property="price", type="number", example=100),
    *    @OA\Property(property="installment", type="integer", example=1),
    *    @OA\Property(property="installment_type", type="string", example="monthly"),
    *    @OA\Property(property="notes", type="string", example="Notlar")
    *    ))
    * )
    */
   public function updateSale() {
      $this->response(function () {
         $request = $this->request->json();
         $dto = new SaleRequest();
         $dto->fromArray($request);
         $result = $this->service->updateSale($dto);
         return $result;
      });
   }

   /**
    * @OA\Delete(
    *    tags={"Sale"}, path="/sale/{id}", summary="Satış sil",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer"))
    * )
    */
   public function deleteSale(int $id) {
      $this->response(function () use ($id) {
         $result = $this->service->delete(
            ['id' => $id,
             'deleted_at' => ['IS NULL']
            ]);
         return $result;
      });
   }
   /**
    * @OA\Get(tags={"Sale"}, path="/sale/{id}/customer", summary="Satış müşteri detayı",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer"))
    * )
    */
   public function getCustomer(int $sale_id) {
      $this->response(function () use ($sale_id) {
         $result = $this->service->getCustomer($sale_id);
         return $result;
      });
   }
   /**
    * @OA\Get(tags={"Sale"}, path="/sale/{id}/installments", summary="Satış taksitleri",
    *    @OA\Response(response=200, description="Success"),
    *    @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer"))
    * )
    */
   public function getInstallments(int $sale_id) {
      $this->response(function () use ($sale_id) {
         $result = $this->service->getInstallments($sale_id);
         return $result;
      });
   }
}
