<?php

declare(strict_types=1);

namespace App\Modules\Sale;

use System\Database\Database;
use App\Core\Abstracts\BaseRepository;

class SaleRepository extends BaseRepository {
   public function __construct(
      protected Database $database,
      protected string $table = 'sale'
   ) {
   }
   public function getInstallments(int $sale_id): array {
      return $this->database
         ->prepare('SELECT *
            FROM installment
            WHERE installment.deleted_at IS NULL
               AND installment.sale_id = :sale_id
         ')
         ->execute([
            'sale_id' => $sale_id,
         ])
         ->fetchAll();
   }
   public function getCustomer(int $sale_id): array {
      return $this->database
         ->prepare('SELECT *
            FROM customer
            WHERE customer.deleted_at IS NULL
               AND customer.id = :customer_id
         ')
         ->execute([
            'customer_id' => $sale_id,
         ])
         ->fetch();
   }
}
