<?php

declare(strict_types=1);

namespace App\Modules\User;

use System\Database\Database;
use App\Core\Abstracts\BaseRepository;

class UserRepository extends BaseRepository {
   public function __construct(
      protected Database $database,
      protected string $table = 'user'
   ) {
   }

   public function findCategory(int $user_id): array {
      return $this->database
         ->prepare('SELECT
               category.id,
               category.title
            FROM user_category

            JOIN category ON category.id = user_category.category_id
               AND category.deleted_at IS NULL
            WHERE user_category.user_id = :user_id
         ')
         ->execute([
            'user_id' => $user_id,
         ])
         ->fetchAll();
   }

   public function findImage(int $user_id): array {
      return $this->database
         ->prepare('SELECT
               user_image.id,
               user_image.user_id,
               user_image.image_path,
               user_image.sort_order,
               user_image.created_at,
               user_image.updated_at,
               user_image.deleted_at,
               user_image.created_by,
               user_image.updated_by,
               user_image.deleted_by,
            FROM user_image
            WHERE user_image.deleted_at IS NULL
               AND user_image.image_path IS NOT NULL
               AND user_image.user_id = :user_id
            ORDER BY user_image.sort_order ASC, user_image.created_at ASC, user_image.id ASC
         ')
         ->execute([
            'user_id' => $user_id,
         ])
         ->fetchAll();
   }
}
