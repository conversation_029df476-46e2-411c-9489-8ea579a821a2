<?php

declare(strict_types=1);

namespace App\Modules\User;

use System\Database\Database;
use System\Validation\Validation;
use App\Core\Abstracts\BaseService;
use System\Exception\SystemException;
use App\Modules\User\UserRepository;

class UserService extends BaseService {
   /** @var UserRepository */
   protected mixed $repository;

   public function __construct(
      protected Database $database,
      protected Validation $validation,
      UserRepository $repository,
   ) {
      $this->repository = $repository;
   }

   public function getAllUser(): array {
      $result = $this->repository->findAll();
      return $result;
   }

   public function getUser(int $id): array {
      $result = $this->repository->findOne($id);
      if (empty($result)) {
         throw new SystemException('Record not found', 404);
      }

      return $result;
   }

   public function createUser(UserRequest $dto): array {
      $existingUsername = $this->repository->findBy(['username' => $dto->username]);
      $existingEmail = $this->repository->findBy(['email' => $dto->email]);

      if (!empty($existingUsername)) {
         throw new SystemException('Username already exists', 400);
      }
      if (!empty($existingEmail)) {
         throw new SystemException('Email already exists', 400);
      }
      return $this->transaction(function () use ($dto) {
         $this->validate($dto->toArray(), [
            'username' => 'required',
            'name' => 'required',
            'surname' => 'required',
            'email' => 'nullable',
            'phone' => 'nullable',
            'password' => 'required',
            'role' => 'required'
         ]);

         $id = $this->create([
            'username' => $dto->username,
            'name' => $dto->name,
            'surname' => $dto->surname,
            'email' => $dto->email,
            'phone' => $dto->phone,
            'password' => $dto->password,
            'role' => $dto->role
         ]);

         return $this->getUser($id);
      });
   }

   public function updateUser(UserRequest $dto): array {
      $existingUsername = $this->repository->findBy(['username' => $dto->username]);
      $existingEmail = $this->repository->findBy(['email' => $dto->email]);

      if (!empty($existingUsername) && $existingUsername[0]['id'] != $dto->id) {
         throw new SystemException('Username already exists', 400);
      }
      if (!empty($existingEmail) && $existingEmail[0]['id'] != $dto->id) {
         throw new SystemException('Email already exists', 400);
      }
      return $this->transaction(function () use ($dto) {
         $this->check([
            'id' => $dto->id
         ]);

         $this->validate($dto->toArray(), [
            'username' => 'required',
            'name' => 'required',
            'surname' => 'required',
            'email' => 'nullable',
            'phone' => 'nullable',
            'password' => 'required',
            'role' => 'required'
         ]);

         $this->update($dto, [
            'username' => $dto->username,
            'name' => $dto->name,
            'surname' => $dto->surname,
            'email' => $dto->email,
            'phone' => $dto->phone,
            'password' => $dto->password,
            'role' => $dto->role
         ], [
            'id' => $dto->id
         ]);

         return $this->getUser($dto->id);
      });
   }
}
