<?php

declare(strict_types=1);

use App\Core\Middlewares\Auth;
use App\Modules\User\UserController;
use App\Modules\Sale\SaleController;
use App\Modules\Image\ImageController;
use App\Modules\Product\ProductController;
use App\Modules\Category\CategoryController;
use App\Modules\Customer\CustomerController;
use App\Modules\Installment\InstallmentController;

/** @var System\Router\Router $router */

// Image routes for image_path
$router->prefix('v1/image')->middleware([Auth::class])->group(function () use ($router) {
   $router->post('/create', [ImageController::class, 'uploadImage']);
   $router->post('/delete', [ImageController::class, 'deleteImage']);
});

// Product routes
$router->prefix('v1/product')->middleware([Auth::class])->group(function () use ($router) {
   $router->get('/', [ProductController::class, 'getAllProduct']);
   $router->post('/', [ProductController::class, 'createProduct']);
   $router->put('/', [ProductController::class, 'updateProduct']);
   $router->delete('/{id}', [ProductController::class, 'deleteProduct'])->where(['id' => '([0-9]+)']);
   $router->get('/{id}', [ProductController::class, 'getProduct'])->where(['id' => '([0-9]+)']);
});

// Category routes
$router->prefix('v1/category')->middleware([Auth::class])->group(function () use ($router) {
   $router->get('/', [CategoryController::class, 'getAllCategory']);
   $router->post('/', [CategoryController::class, 'createCategory']);
   $router->put('/', [CategoryController::class, 'updateCategory']);
   $router->delete('/{id}', [CategoryController::class, 'deleteCategory'])->where(['id' => '([0-9]+)']);
   $router->get('/{id}', [CategoryController::class, 'getCategoryById'])->where(['id' => '([0-9]+)']);
});

// User routes
$router->prefix('v1/user')->middleware([Auth::class])->group(function () use ($router) {
   $router->get('/', [UserController::class, 'getAllUser']);
   $router->get('/{id}', [UserController::class, 'getUser'])->where(['id' => '([0-9]+)']);
   $router->post('/', [UserController::class, 'createUser']);
   $router->put('/', [UserController::class, 'updateUser']);
   $router->delete('/{id}', [UserController::class, 'deleteUser'])->where(['id' => '([0-9]+)']);
});

// Customer routes
$router->prefix('v1/customer')->middleware([Auth::class])->group(function () use ($router) {
   $router->get('/', [CustomerController::class, 'getAllCustomer']);
   $router->get('/{id}', [CustomerController::class, 'getCustomer'])->where(['id' => '([0-9]+)']);
   $router->post('/', [CustomerController::class, 'createCustomer']);
   $router->put('/', [CustomerController::class, 'updateCustomer']);
   $router->delete('/{id}', [CustomerController::class, 'deleteCustomer'])->where(['id' => '([0-9]+)']);
   $router->get('/{id}/sales', [CustomerController::class, 'getSales'])->where(['id' => '([0-9]+)']);
   $router->get('/{id}/installments', [CustomerController::class, 'getInstallments'])->where(['id' => '([0-9]+)']);
});

// Sale routes
$router->prefix('v1/sale')->middleware([Auth::class])->group(function () use ($router) {
   $router->get('/', [SaleController::class, 'getAllSale']);
   $router->get('/{id}', [SaleController::class, 'getSale'])->where(['id' => '([0-9]+)']);
   $router->post('/', [SaleController::class, 'createSale']);
   $router->put('/', [SaleController::class, 'updateSale']);
   $router->delete('/{id}', [SaleController::class, 'deleteSale'])->where(['id' => '([0-9]+)']);
   $router->get('/{id}/customer', [SaleController::class, 'getCustomer'])->where(['id' => '([0-9]+)']);
   $router->get('/{id}/installments', [SaleController::class, 'getInstallments'])->where(['id' => '([0-9]+)']);
});

// Instalment routes
$router->prefix('v1/installment')->middleware([Auth::class])->group(function () use ($router) {
   $router->get('/', [InstallmentController::class, 'getAllInstallment']);
   $router->get('/{id}', [InstallmentController::class, 'getInstallment'])->where(['id' => '([0-9]+)']);
   $router->post('/', [InstallmentController::class, 'createInstallment']);
   $router->put('/', [InstallmentController::class, 'updateInstallment']);
   $router->delete('/{id}', [InstallmentController::class, 'deleteInstallment'])->where(['id' => '([0-9]+)']);
});
