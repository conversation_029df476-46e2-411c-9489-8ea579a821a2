<template>
   <v-btn
      v-bind:="$attrs"
      class="text-neutral-400 not-dark:text-neutral-500"
      density="default"
      prepend-icon="$plus"
      variant="outlined">
      <template v-if="$attrs.text">
         {{ $attrs.text }}
      </template>
      <template v-else>
         <slot />
      </template>
   </v-btn>
</template>

<script lang="ts" setup>
import type { TBtn } from "@/utils/vuetify";

withDefaults(defineProps<TBtn>(), {});
</script>
