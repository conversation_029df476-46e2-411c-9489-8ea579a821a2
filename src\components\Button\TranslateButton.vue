<template>
   <v-btn
      v-bind:loading="isLoading"
      v-bind:ripple="false"
      color="primary"
      density="compact"
      icon="$translate"
      size="small"
      variant="plain"
      @click="translateHandler" />
</template>

<script lang="ts" setup>
const model = defineModel({ type: String, default: "" });
const { translate, isLoading } = useTranslate();

const props = defineProps({
   from: {
      type: String,
      default: "auto"
   },
   to: {
      type: String,
      default: "en"
   }
});

const translateHandler = async () => {
   model.value = await translate(model.value, props.from, props.to);
};
</script>
