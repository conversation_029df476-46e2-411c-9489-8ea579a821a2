<template>
   <div
      v-if="$slots.header"
      class="flex h-12 justify-between gap-2">
      <slot name="header" />
   </div>

   <v-card
      v-bind="{ ...$attrs }"
      v-bind:loading="props.loading"
      class="mb-4 overflow-visible">
      <div
         v-if="$slots.extension"
         class="flex justify-between gap-2 overflow-hidden rounded-t-sm">
         <slot name="extension" />
      </div>

      <slot />
   </v-card>
</template>

<script lang="ts" setup>
import type { TCard } from "@/utils/vuetify";

type TProps = {
   loading?: boolean;
};

const props = withDefaults(defineProps<TCard & TProps>(), {
   loading: false
});
</script>
