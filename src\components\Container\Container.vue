<template>
   <v-container
      v-bind="{ ...$attrs }"
      class="overflow-visible">
      <v-skeleton-loader v-if="props.loading" />

      <div v-else-if="props.error">Aradığınız sayfa bulunamadı</div>
      <template v-else>
         <v-form
            v-if="props.form"
            ref="formRef"
            @submit.prevent="handleSubmit">
            <slot />
         </v-form>
         <slot v-else />
      </template>
   </v-container>
</template>

<script lang="ts" setup>
import type { TContainer } from "@/utils/vuetify";

const formRef = ref<any>();

type TProps = {
   loading?: boolean;
   error?: boolean;
   form?: (e?: Event) => void | Promise<void>;
};

const props = withDefaults(defineProps<TContainer & TProps>(), {
   loading: false,
   error: false
});

const handleSubmit = async (e?: Event) => {
   if (!props.form) return;

   if (formRef.value) {
      const { valid } = await formRef.value.validate();
      if (!valid) return;
   }
   await props.form(e);
};

defineExpose({
   form: formRef,
   validate: () => formRef.value?.validate(),
   isValid: () => formRef.value?.isValid,
});
</script>
