<template>
   <v-menu
      v-model="dateMenu"
      v-bind:close-on-content-click="false"
      v-bind:offset="props.hideDetails ? [0, 0] : [-22, 0]"
      class="[&_.v-overlay\_\_content]:!min-w-min"
      eager
      transition="fade-transition">
      <template v-slot:activator="{ props: activatorProps }">
         <v-text-field
            v-bind="{ ...activatorProps, ...$attrs }"
            v-bind:active="dateMenu"
            v-bind:hide-details="props.hideDetails"
            v-bind:model-value="dateDisplay"
            v-bind:readonly="props.readonly"
            v-bind:title="props.title"
            @click:clear="dateMenu = !!props.openOnClear"
            @update:model-value="model = null!" />
      </template>

      <v-card class="overflow-x-hidden">
         <v-card-title
            v-if="props.title || $slots.title"
            class="pb-0 text-base">
            <slot
               v-bind:model
               name="title">
               <template v-if="props.title === true">
                  {{ formatDate(model, props.format) || t("app.noDate") }}
               </template>

               <template v-else-if="props.title">
                  {{ props.title }}
               </template>
            </slot>
         </v-card-title>

         <v-date-picker
            v-model="model"
            v-bind="{ ...$attrs }"
            v-bind:first-day-of-week="props.firstDayOfWeek"
            v-bind:show-adjacent-months="props.showAdjacentMonths"
            color="primary"
            hide-header
            width="300"
            @update:model-value="dateHandler($event)" />

         <div
            v-if="props.actions"
            class="flex justify-end gap-2 px-2 pb-2">
            <v-btn
               color="error"
               density="default"
               variant="tonal"
               @click="dateMenu = false">
               {{ t("app.cancel") }}
            </v-btn>

            <v-spacer></v-spacer>

            <v-btn
               density="default"
               variant="plain"
               @click="dateHandler(new Date(), 0)">
               {{ t("app.today") }}
            </v-btn>

            <v-btn
               density="default"
               variant="plain"
               @click="dateHandler(new Date(), 1)">
               {{ t("app.tomorrow") }}
            </v-btn>
         </div>
      </v-card>
   </v-menu>
</template>

<script lang="ts" setup>
import type { TDateField } from "@/utils/vuetify";

type TProps = {
   title?: boolean | string;
   readonly?: boolean;
   actions?: boolean;
   hideDetails?: boolean;
   firstDayOfWeek?: number;
   showAdjacentMonths?: boolean;
   closeOnPickerClick?: boolean;
   openOnClear?: boolean;
   color?: string;
   format?: Intl.DateTimeFormatOptions;
};

const props = withDefaults(defineProps<Omit<TDateField, "title"> & TProps>(), {
   readonly: true,
   actions: true,
   hideDetails: false,
   closeOnPickerClick: true,
   showAdjacentMonths: true,
   firstDayOfWeek: 1,
   openOnClear: true,
   format: () => ({ weekday: "short" })
});

defineOptions({ inheritAttrs: false });

const model = defineModel({ type: [Date, Object, null], default: null });
const { t } = useI18n();

const dateMenu = ref(false);
const dateDisplay = computed(() => formatDate(model.value));

const dateHandler = (value: Date, day?: number) => {
   if (typeof day === "number") {
      value.setDate(value.getDate() + day);
   }

   model.value = formatMS(value);
   dateMenu.value = !props.closeOnPickerClick;
};
</script>
