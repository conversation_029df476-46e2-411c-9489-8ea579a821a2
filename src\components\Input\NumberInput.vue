<template>
   <v-text-field
      v-model="model"
      v-maska="money"
      class="[&_.v-field--appended]:pe-0"
      @blur="model = formatFraction(String(model || '0'))"
      @focus="handleFocus()">
      <template v-slot:append-inner>
         <v-divider vertical />
         <v-btn
            v-bind:ripple="false"
            class="h-full rounded-none"
            density="compact"
            icon="$expand"
            variant="text"
            @click="decrease"
            @mousedown.stop></v-btn>
         <v-divider vertical />
         <v-btn
            v-bind:ripple="false"
            class="h-full rounded-none"
            density="compact"
            icon="$collapse"
            variant="text"
            @click="increase"
            @mousedown.stop></v-btn>
      </template>
   </v-text-field>
</template>

<
<script lang="ts" setup>
import { vMaska } from "maska/vue";

const model = defineModel({ type: String, default: "0" });

const props = defineProps({
   step: {
      type: Number,
      default: 0.01
   }
});

const money = {
   number: {
      locale: "tr",
      fraction: 2,
      unsigned: true
   },
   eager: true,
   reversed: true
};

const increase = (event: MouseEvent) => {
   const value = formatDecimal(model.value || "0");
   let step = props.step;
   if (event.shiftKey) {
      step = 0.1;
   }

   model.value = (value + step).toLocaleString("tr-TR", { minimumFractionDigits: 2 });
};

const decrease = (event: MouseEvent) => {
   const value = formatDecimal(model.value || "0");
   let step = props.step;
   if (event.shiftKey) {
      step = 0.1;
   }
   model.value = (value - step).toLocaleString("tr-TR", { minimumFractionDigits: 2 });
};

const handleFocus = () => {
   const stringValue = String(model.value || "0");
   model.value = stringValue.replace(/(\,\d*?)0+$/, "$1").replace(/,\s*$/, ",");
};
</script>
