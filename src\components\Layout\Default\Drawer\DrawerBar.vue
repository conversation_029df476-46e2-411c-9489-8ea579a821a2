<template>
   <v-navigation-drawer
      v-bind:class="{ '!w-60': expand }"
      class="bg-dark pt-2 select-none [&_.v-navigation-drawer\_\_content]:flex [&_.v-navigation-drawer\_\_content]:flex-col"
      color="background"
      rail
      theme="dark">
      <div class="flex-center w-14">
         <v-btn
            v-bind:active="expand"
            icon
            @click="expand = !expand">
            <ToggleIcon
               v-bind:icon="['$menu', '$close']"
               v-bind:toggle="!expand" />
         </v-btn>
      </div>

      <v-list
         class="flex flex-1 flex-col"
         density="compact">
         <v-list-item
            v-for="item in appMenu"
            v-bind="item.itemProps"
            v-bind:active="appStore.module === item.itemProps?.value"
            @click="moduleHandler(item.itemProps?.value)">
            <template v-slot:prepend>
               <v-icon
                  v-bind:icon="item.itemProps?.prependIcon"
                  size="default" />
            </template>

            <v-list-item-title>{{ t(item.itemTitle as string) }}</v-list-item-title>
         </v-list-item>

         <v-spacer />
         <v-list-item link>
            <template v-slot:prepend>
               <v-icon
                  icon="$accountProfile"
                  size="default" />
            </template>

            <v-list-item-title>
               {{ t("account.profile") }}
            </v-list-item-title>
         </v-list-item>
         <v-list-item link>
            <template v-slot:prepend>
               <v-icon
                  icon="$settings"
                  size="default" />
            </template>

            <v-list-item-title>
               {{ t("app.settings") }}
            </v-list-item-title>
         </v-list-item>
      </v-list>
   </v-navigation-drawer>
</template>

<script lang="ts" setup>
import ToggleIcon from "@/components/Input/ToggleIcon.vue";

const appStore = useAppStore();
const expand = ref(false);
const { t } = useI18n();

const moduleHandler = (value: string) => {
   appStore.setModule(value);
   expand.value = false;
};
</script>
