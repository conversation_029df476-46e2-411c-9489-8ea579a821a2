<template>
   <v-breadcrumbs class="text-xs">
      <template
         v-for="(item, index) in items"
         v-bind:key="item">
         <v-breadcrumbs-item
            v-bind:class="{ 'font-semibold': index === items.length - 1 }"
            v-bind:exact="appConfig.router.exact"
            v-bind:to="!(index === items.length - 1) ? item.path : undefined">
            {{ t(item.breadcrumb, 2) }}
         </v-breadcrumbs-item>

         <v-icon
            v-if="index < items.length - 1"
            size="x-small"
            icon="$chevronRight" />
      </template>
   </v-breadcrumbs>
</template>

<script lang="ts" setup>
import { useAppStore } from "@/stores/appStore";
const appStore = useAppStore();
const { t } = useI18n();
const route = useRoute();

const items = computed(() => {
   return route.matched
      .filter((item) => {
         return item.meta?.breadcrumb;
      })
      .map((item) => {
         const raw = item.meta.breadcrumb as string;
         const match = raw.match(/^{(.+)}$/);
         const breadcrumb = match ? appStore.breadcrumb[match[1]] || raw : raw;

         return { breadcrumb, path: item.path };
      });
});
</script>
