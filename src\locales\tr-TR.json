{"app": {"confirmTitle": "İşlemi Onaylayın", "promptTitle": "<PERSON><PERSON><PERSON>", "warningTitle": "Uyarı", "errorTitle": "<PERSON><PERSON>", "noData": "Veri Bulunamadı", "noDate": "<PERSON><PERSON><PERSON>", "createDate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "updateDate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "date": "<PERSON><PERSON><PERSON>", "today": "<PERSON><PERSON><PERSON><PERSON>", "tomorrow": "<PERSON><PERSON><PERSON><PERSON>", "yesterday": "<PERSON><PERSON><PERSON>", "ok": "<PERSON><PERSON>", "yes": "<PERSON><PERSON>", "no": "Hay<PERSON><PERSON>", "close": "Ka<PERSON><PERSON>", "cancel": "Vazgeç", "accept": "<PERSON><PERSON><PERSON>", "decline": "<PERSON><PERSON>", "show": "<PERSON><PERSON><PERSON>", "showAll": "Tümünü <PERSON>ö<PERSON>", "hide": "<PERSON><PERSON><PERSON>", "hideAll": "Tümünü <PERSON>", "select": "<PERSON><PERSON><PERSON>", "selectAll": "Tümünü Seç", "search": "Ara", "add": "<PERSON><PERSON>", "remove": "Kaldır", "create": "Oluştur", "update": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Sil", "detail": "Detay", "value": "<PERSON><PERSON><PERSON>", "list": "Liste", "filter": "Filtre", "clear": "<PERSON><PERSON><PERSON>", "reset": "Sıfırla", "refresh": "<PERSON><PERSON><PERSON>", "back": "<PERSON><PERSON>", "forward": "İleri", "previous": "<PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON>", "theme": "<PERSON><PERSON>", "save": "<PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON>", "copy": "Kopyala", "paste": "Yapıştır", "cut": "<PERSON><PERSON>", "undo": "<PERSON><PERSON>", "redo": "<PERSON><PERSON>", "find": "Bul", "print": "Yazdır", "export": "Dışa Aktar", "import": "İçe Aktar", "download": "<PERSON><PERSON><PERSON>", "upload": "<PERSON><PERSON><PERSON>", "active": "Aktif", "passive": "<PERSON><PERSON><PERSON>", "safe": "<PERSON><PERSON><PERSON><PERSON>", "risky": "<PERSON><PERSON>", "actions": "İşlemler", "code": "Kod", "name": "Ad<PERSON>", "title": "Başlık", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "content": "İçerik", "message": "<PERSON><PERSON>", "status": "Durum", "type": "Tip", "link": "Link", "url": "URL", "sort_order": "Sıralama", "price": "<PERSON><PERSON><PERSON>", "currentDebit": "<PERSON><PERSON><PERSON><PERSON>", "riskStatus": "<PERSON>", "stock": "Stok", "currency": "Para Birimi", "metaTitle": "Meta Başlık", "metaDescription": "Meta Açıklama", "metaKeywords": "<PERSON><PERSON>", "image": "Görsel | Görseller", "deleteImage": "<PERSON>u görseli silmek istediğinize emin misiniz?", "imageDeleted": "<PERSON><PERSON><PERSON><PERSON>", "file": "Dosya | Dosyalar", "deleteFile": "Bu dosyayı silmek istediğinize emin misiniz?", "fileDeleted": "<PERSON><PERSON><PERSON>", "language": "Dil", "settings": "<PERSON><PERSON><PERSON>", "definitions": "Tan<PERSON>mlar", "basicInfo": "<PERSON><PERSON>", "product": "Ürün | Ürünler", "user": "Kullanıcı | Kullanıcılar", "customer": "Müşteri | Müşteriler", "sale": "Satış | Satışlar", "category": "Kategori | Kategoriler", "manufacturer": "Marka | Markalar", "attribute": "Özellik | Özellikler", "standard": "Standart | Standartlar", "recordSuccess": "İşlem Başarılı", "recordFailed": "İşlem Başarısız", "recordNotFound": "Kayıt Bulunamadı", "recordDeleted": "<PERSON><PERSON><PERSON>", "recordCreated": "<PERSON><PERSON>t <PERSON>", "recordUpdated": "<PERSON><PERSON><PERSON>", "recordReset": "<PERSON><PERSON>t <PERSON>ırlandı", "selectOrDropImage": "<PERSON><PERSON><PERSON><PERSON> veya Buraya Sürükleyin", "selectOrDropFile": "<PERSON><PERSON><PERSON> veya Buraya <PERSON>ü<PERSON>", "productList": "<PERSON><PERSON><PERSON><PERSON>", "productDetail": "<PERSON><PERSON><PERSON><PERSON>", "createProduct": "<PERSON><PERSON><PERSON><PERSON>", "updateProduct": "<PERSON><PERSON><PERSON><PERSON>", "deleteProduct": "<PERSON><PERSON><PERSON><PERSON>", "categoryList": "<PERSON><PERSON><PERSON>", "categoryDetail": "<PERSON><PERSON><PERSON>", "createCategory": "<PERSON><PERSON><PERSON>", "updateCategory": "<PERSON><PERSON><PERSON>", "deleteCategory": "<PERSON><PERSON><PERSON>", "manufacturerList": "<PERSON><PERSON>", "manufacturerDetail": "<PERSON><PERSON>", "createManufacturer": "<PERSON><PERSON>", "updateManufacturer": "<PERSON><PERSON>", "deleteManufacturer": "<PERSON><PERSON>", "attrList": "Özellik Listesi", "attrDetail": "Özellik Detayı", "createAttr": "Özellik Oluştur", "updateAttr": "Özellik Güncelle", "deleteAttr": "Özellik Sil", "standardList": "<PERSON><PERSON>", "standardDetail": "<PERSON><PERSON>", "createStandard": "<PERSON><PERSON>", "updateStandard": "<PERSON><PERSON>", "deleteStandard": "<PERSON><PERSON>", "customerList": "Müşteri <PERSON>esi", "customerDetail": "Müşteri Detayı", "createCustomer": "Müşteri Oluştur", "updateCustomer": "Müşteri Güncelle", "deleteCustomer": "Müşteri Sil", "saleList": "Satış <PERSON>esi", "saleDetail": "Satış Detayı", "createSale": "Satış Oluştur", "updateSale": "Satış Güncelle", "deleteSale": "Satış Sil", "installment": "<PERSON><PERSON><PERSON>", "installmentType": "<PERSON>ks<PERSON>", "monthly": "Aylık", "weekly": "Haftalık", "daily": "Günlük", "surname": "Soyadı", "email": "E-posta", "phone": "Telefon", "tckn": "TC Kimlik No", "address": "<PERSON><PERSON>", "current_debit": "<PERSON><PERSON><PERSON><PERSON>", "is_risky": "<PERSON><PERSON>", "notes": "Notlar", "competingList": "Rakip <PERSON>", "competingDetail": "Rakip Ürün <PERSON>", "createCompeting": "Rakip Ürü<PERSON>", "updateCompeting": "Rakip <PERSON>", "deleteCompeting": "Rakip Ü<PERSON>ü<PERSON>", "isCompeting": "<PERSON><PERSON><PERSON>"}, "account": {"profile": "Profil", "login": "Giriş Yap", "logout": "Çıkış", "register": "<PERSON><PERSON><PERSON>"}, "language": {"turkish": "Türkçe", "english": "İngilizce", "german": "Almanca"}}