<template>
   <Container
      v-bind:error="isError"
      v-bind:form="formHandler"
      v-bind:loading="isLoading">
      <Card v-bind:loading="isLoading || isPending">
         <template v-slot:header>
            <v-card-title class="flex-center p-0 text-base">{{ title }}</v-card-title>
            <v-spacer />
            <ActionButton
               v-if="category.id"
               v-bind:disabled="isLoading || isPending"
               color="error"
               prepend-icon="$trash"
               @click="removeHandler">
               {{ t("app.delete") }}
            </ActionButton>
            <ActionButton
               v-if="!isSuccess"
               v-bind:disabled="isLoading || isPending"
               type="submit"
               prepend-icon="$save">
               {{ isCreate ? t("app.save") : t("app.update") }}
            </ActionButton>
         </template>

         <template v-slot:extension>
            <v-card-title class="text-base">{{ t("app.basicInfo") }}</v-card-title>
            <LanguageTab
               v-model="language"
               v-bind:loading="isLoading" />
         </template>

         <v-card-text>
            <v-row no-gutters>
               <v-col md="4">
                  <v-list-subheader>{{ t("app.code") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     v-model="category.code"
                     v-bind:rules="[appRules.required()]" />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.title") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     v-model="category.title"
                     v-bind:rules="[appRules.required()]">
                     <template
                        v-if="language !== 1"
                        v-slot:append-inner>
                        <TranslateButton v-model="category.title" />
                     </template>
                  </v-text-field>
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.description") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-textarea
                     v-model="category.content"
                     v-bind:rules="[appRules.required()]"
                     class="[&_.v-field\_\_input]:min-h-[min(var(--v-input-control-height,56px),364px)]"
                     auto-grow
                     no-resize>
                     <template
                        v-if="language !== 1"
                        v-slot:append-inner>
                        <TranslateButton v-model="category.content" />
                     </template>
                  </v-textarea>
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.status") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-switch
                     v-model="category.is_active"
                     v-bind:false-value="0"
                     v-bind:ripple="false"
                     v-bind:true-value="1"
                     color="primary"
                     density="compact">
                     <template v-slot:label>
                        <div class="text-sm">{{ category.is_active ? t("app.active") : t("app.passive") }}</div>
                     </template>
                  </v-switch>
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.image") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <ImageList
                     v-bind:delete="deleteImageHandler"
                     v-bind:items="[category.image_path]" />
                  <ImageUpload v-model="imageUpload" />
               </v-col>
            </v-row>
         </v-card-text>
      </Card>
   </Container>
</template>

<script lang="ts" setup>
import ActionButton from "@/components/Button/ActionButton.vue";
import TranslateButton from "@/components/Button/TranslateButton.vue";
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import ImageList from "@/components/Input/ImageList.vue";
import ImageUpload from "@/components/Input/ImageUpload.vue";
import LanguageTab from "@/components/Tab/LanguageTab.vue";
import { ICategory, ICategoryStore, useCreateCategory, useGetCategoryById, useUpdateCategory, useDeleteCategory } from "../services/CategoryService";
import { useDeleteImage, useUploadImage } from "../services/ImageService";

const { t } = useI18n();
const route = useRoute() as TRoute;
const appStore = useAppStore();
const snackbarStore = useSnackbarStore();
const confirmStore = useConfirmStore();

// category
const category = ref({
   is_active: 1,
   sort_order: 1
} as ICategory);
const routeId = computed(() => route.params.id);
const isCreate = computed(() => routeId.value === "create");
const isEnabled = computed(() => !!routeId.value && !isCreate.value);
const language = ref(1);
const title = computed(() => (isCreate.value ? t("app.createCategory") : t("app.categoryDetail")));
const imageUpload = ref([] as File[]);

// set breadcrumb
appStore.setBreadcrumb("CategoryDetail", title);

// services
const getCategoryById = useGetCategoryById({
   id: routeId,
   enabled: isEnabled,
   language: language,
   onSuccess: (data) => {
      category.value = { ...data };
   }
});
const updateCategory = useUpdateCategory();
const createCategory = useCreateCategory();
const deleteCategory = useDeleteCategory();
const uploadImage = useUploadImage();
const deleteImage = useDeleteImage({ invalidate: ["category", "categoryById"] });

// loading
const isLoading = computed(() => getCategoryById.isLoading.value);
const isPending = computed(() => createCategory.isPending.value || updateCategory.isPending.value);
const isError = computed(() => getCategoryById.isError.value);
const isSuccess = computed(() => createCategory.isSuccess.value);

// handlers
const deleteCategoryImage = async () => {
   return await deleteImage.mutateAsync({
      id: category.value.id,
      path: category.value.image_path,
      table: "asset_category"
   });
};

const uploadCategoryImage = async () => {
   return await uploadImage.mutateAsync({
      files: imageUpload.value,
      path: "asset_category"
   });
};

const deleteImageHandler = async () => {
   try {
      const confirm = await confirmStore.open({
         title: t("app.confirmTitle"),
         message: t("app.deleteImage")
      });

      if (confirm) {
         await deleteCategoryImage();
         snackbarStore.add({ text: t("app.imageDeleted") });
      }
   } catch {
      snackbarStore.add({ text: t("app.recordFailed"), color: "error" });
   } finally {
      confirmStore.close();
   }
};

const removeHandler = async () => {
   try {
      const confirm = await confirmStore.open({
         title: t("app.confirmTitle"),
         message: t("app.deleteCategory")
      });

      if (confirm) {
         await deleteCategory.mutateAsync(category.value.id);
         snackbarStore.add({ text: t("app.recordDeleted") });
         router.push({ name: "categoryList" });
      }
   } catch {
      snackbarStore.add({ text: t("app.recordFailed"), color: "error" });
   } finally {
      confirmStore.close();
   }
};

const formHandler = async () => {
   const payload: ICategoryStore = {
      code: category.value.code,
      title: category.value.title,
      content: category.value.content,
      is_active: category.value.is_active,
      sort_order: category.value.sort_order
   };

   try {
      if (imageUpload.value.length) {
         if (category.value.image_path) {
            await deleteCategoryImage();
            snackbarStore.add({ text: t("app.imageDeleted") });
         }

         const upload = await uploadCategoryImage();
         payload.image_path = upload.data[0];
         imageUpload.value = [];
      }

      if (isCreate.value) {
         await createCategory.mutateAsync(payload, {
            onSuccess: (data) => {
               router.push({ name: "categoryList", params: { id: data.data.id } });
            }
         });
         snackbarStore.add({ text: t("app.recordCreated") });
      } else {
         await updateCategory.mutateAsync({ id: category.value.id, ...payload });
         snackbarStore.add({ text: t("app.recordUpdated") });
      }
   } catch (error: any) {
      snackbarStore.add({ text: error.data.error, color: "error" });
   }
};
</script>
