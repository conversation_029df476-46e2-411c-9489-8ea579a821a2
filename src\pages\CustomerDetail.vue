<template>
   <Container v-bind:error="isError" v-bind:form="formHandler" v-bind:loading="isLoading">
      <Card v-bind:loading="isLoading || isPending">
         <template v-slot:header>
            <v-card-title class="flex-center p-0 text-base">{{ title }}</v-card-title>
            <v-spacer />
            <ActionButton v-if="customer.id" v-bind:disabled="isLoading || isPending" color="error" prepend-icon="$trash" @click="removeHandler">
               {{ t("app.delete") }}
            </ActionButton>
            <ActionButton v-if="!isSuccess" v-bind:disabled="isLoading || isPending" type="submit" prepend-icon="$save">
               {{ customer.id ? t("app.update") : t("app.save") }}
            </ActionButton>
         </template>

         <template v-slot:extension>
            <v-card-title class="text-base">{{ t("app.basicInfo") }}</v-card-title>
         </template>

         <v-card-text>
            <v-row no-gutters>
               <v-col md="4">
                  <v-list-subheader>{{ t("app.name") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field v-model="customer.name" v-bind:rules="[appRules.required()]" />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.surname") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field v-model="customer.surname" v-bind:rules="[appRules.required()]" />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.phone") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field v-model="customer.phone" v-bind:rules="[appRules.required()]" />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.email") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field v-model="customer.email" v-bind:rules="[appRules.email()]" />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.tckn") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field v-model="customer.tckn" />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.address") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-textarea v-model="customer.address" auto-grow no-resize />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.currentDebit") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <NumberInput v-model="customer.current_debit" v-bind:step="0.01" />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.notes") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-textarea v-model="customer.notes" auto-grow no-resize />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.status") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-switch v-model="customer.is_active" v-bind:false-value="0" v-bind:ripple="false" v-bind:true-value="1" color="primary" density="compact">
                     <template v-slot:label>
                        <div class="text-sm">{{ customer.is_active ? t("app.active") : t("app.passive") }}</div>
                     </template>
                  </v-switch>
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.riskStatus") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-switch v-model="customer.is_risky" v-bind:false-value="0" v-bind:ripple="false" v-bind:true-value="1" color="error" density="compact">
                     <template v-slot:label>
                        <div class="text-sm">{{ customer.is_risky ? t("app.risky") : t("app.safe") }}</div>
                     </template>
                  </v-switch>
               </v-col>
            </v-row>
         </v-card-text>
      </Card>
   </Container>
</template>

<script lang="ts" setup>
import ActionButton from "@/components/Button/ActionButton.vue";
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import NumberInput from "@/components/Input/NumberInput.vue";
import { appRules } from "@/utils/rules";
import { ICustomer, ICustomerStore, useCreateCustomer, useGetCustomerById, useUpdateCustomer, useDeleteCustomer } from "../services/CustomerService";

const { t } = useI18n();
const route = useRoute() as TRoute;
const appStore = useAppStore();
const snackbarStore = useSnackbarStore();
const confirmStore = useConfirmStore();

// customer
const customer = ref({
   is_active: 1,
   is_risky: 0
} as ICustomer);
const routeId = computed(() => route.params.id);
const isCreate = computed(() => routeId.value === "create");
const isEnabled = computed(() => !!routeId.value && !isCreate.value);
const title = computed(() => (isCreate.value ? t("app.createCustomer") : t("app.customerDetail")));

// set breadcrumb
appStore.setBreadcrumb("CustomerDetail", title);

// services
const getCustomerById = useGetCustomerById({
   id: routeId,
   enabled: isEnabled,
   onSuccess: (item) => {
      customer.value = { ...item };
   }
});
const updateCustomer = useUpdateCustomer();
const createCustomer = useCreateCustomer();
const deleteCustomer = useDeleteCustomer();

// loading
const isLoading = computed(() => getCustomerById.isLoading.value);
const isPending = computed(() => createCustomer.isPending.value || updateCustomer.isPending.value);
const isError = computed(() => getCustomerById.isError.value);
const isSuccess = computed(() => createCustomer.isSuccess.value);

// handlers
const formHandler = async () => {
   const payload: ICustomerStore = {
      name: customer.value.name,
      surname: customer.value.surname,
      email: customer.value.email,
      phone: customer.value.phone,
      tckn: customer.value.tckn,
      address: customer.value.address,
      current_debit: formatDecimal(customer.value.current_debit),
      is_active: customer.value.is_active,
      is_risky: customer.value.is_risky,
      notes: customer.value.notes
   };

   try {
      if (isCreate.value) {
         await createCustomer.mutateAsync(payload);
         snackbarStore.add({ text: t("app.recordCreated") });
      } else {
         await updateCustomer.mutateAsync({ id: customer.value.id, ...payload });
         snackbarStore.add({ text: t("app.recordUpdated") });
      }
   } catch (error) {
      snackbarStore.add({ text: t("app.recordFailed"), color: "error" });
   }
};
const removeHandler = async () => {
   try {
      const confirm = await confirmStore.open({
         title: t("app.confirmTitle"),
         message: t("app.deleteCustomer")
      });

      if (confirm) {
         await deleteCustomer.mutateAsync(customer.value.id);
         snackbarStore.add({ text: t("app.recordDeleted") });
         router.push({ name: "customerList" });
      }
   } catch {
      snackbarStore.add({ text: t("app.recordFailed"), color: "error" });
   } finally {
      confirmStore.close();
   }
};
</script>