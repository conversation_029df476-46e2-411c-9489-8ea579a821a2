<template>
   <Container v-bind:error="isError" v-bind:loading="isLoading">
      <!-- <PERSON><PERSON>ş<PERSON>i Bilgileri Kartı -->
      <Card v-bind:loading="isLoading">
         <template v-slot:header>
            <v-card-title class="flex-center p-0 text-base">{{ t("app.customerHistory") }}</v-card-title>
         </template>

         <template v-slot:extension>
            <v-card-title class="text-base">{{ t("app.customerInfo") }}</v-card-title>
         </template>

         <v-card-text v-if="customer">
            <v-row no-gutters>
               <v-col md="3">
                  <v-list-subheader>{{ t("app.name") }}</v-list-subheader>
               </v-col>
               <v-col md="3">
                  <div class="text-body-1">{{ customer.name }} {{ customer.surname }}</div>
               </v-col>
               <v-col md="3">
                  <v-list-subheader>{{ t("app.phone") }}</v-list-subheader>
               </v-col>
               <v-col md="3">
                  <div class="text-body-1">{{ customer.phone }}</div>
               </v-col>
               <v-col md="3">
                  <v-list-subheader>{{ t("app.email") }}</v-list-subheader>
               </v-col>
               <v-col md="3">
                  <div class="text-body-1">{{ customer.email || '-' }}</div>
               </v-col>
               <v-col md="3">
                  <v-list-subheader>{{ t("app.currentDebit") }}</v-list-subheader>
               </v-col>
               <v-col md="3">
                  <div class="text-body-1 font-weight-bold" :class="parseFloat(customer.current_debit) > 0 ? 'text-error' : 'text-success'">
                     {{ formatCurrency(customer.current_debit) }}
                  </div>
               </v-col>
            </v-row>
         </v-card-text>
      </Card>

      <!-- Satışlar Kartı -->
      <Card v-bind:loading="salesLoading" class="mt-4">
         <template v-slot:header>
            <v-card-title class="flex-center p-0 text-base">{{ t("app.salesHistory") }}</v-card-title>
         </template>

         <v-card-text>
            <DataTable
               v-bind:headers="salesHeaders"
               v-bind:items="customerSales"
               v-bind:loading="salesLoading">
               <template v-slot:item.price="{ value }">
                  <div class="font-weight-bold">{{ formatCurrency(value) }}</div>
               </template>
               <template v-slot:item.installment_type="{ value }">
                  <v-chip size="small" :color="getInstallmentTypeColor(value)">{{ value }}</v-chip>
               </template>
               <template v-slot:item.created_at="{ value }">
                  <div>{{ formatDate(value) }}</div>
               </template>
            </DataTable>
         </v-card-text>
      </Card>

      <!-- Taksitler Kartı -->
      <Card v-bind:loading="installmentsLoading" class="mt-4">
         <template v-slot:header>
            <v-card-title class="flex-center p-0 text-base">{{ t("app.installmentsHistory") }}</v-card-title>
         </template>

         <v-card-text>
            <DataTable
               v-bind:headers="installmentsHeaders"
               v-bind:items="customerInstallments"
               v-bind:loading="installmentsLoading">
               <template v-slot:item.amount="{ value }">
                  <div class="font-weight-bold">{{ formatCurrency(value) }}</div>
               </template>
               <template v-slot:item.payment="{ value }">
                  <div class="font-weight-bold text-success">{{ formatCurrency(value) }}</div>
               </template>
               <template v-slot:item.status="{ value }">
                  <v-chip size="small" :color="getInstallmentStatusColor(value)">{{ getInstallmentStatusText(value) }}</v-chip>
               </template>
               <template v-slot:item.due_at="{ value }">
                  <div>{{ value ? formatDate(value) : '-' }}</div>
               </template>
               <template v-slot:item.paid_at="{ value }">
                  <div>{{ value ? formatDate(value) : '-' }}</div>
               </template>
               <template v-slot:item.sale="{ value }">
                  <div>{{ value?.product || '-' }}</div>
               </template>
            </DataTable>
         </v-card-text>
      </Card>
   </Container>
</template>

<script lang="ts" setup>
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import DataTable from "@/components/Table/DataTable.vue";
import { ICustomer, useGetCustomerById } from "../services/CustomerService";
import { IInstallment, useGetInstallmentAll } from "../services/InstallmentService";
import { ISale, useGetSaleAll } from "../services/SaleService";

const { t } = useI18n();
const route = useRoute() as TRoute;
const appStore = useAppStore();

// customer
const customer = ref<ICustomer | null>(null);
const routeId = computed(() => route.params.id as string);

// set breadcrumb
appStore.setBreadcrumb("CustomerHistory", t("app.customerHistory"));

// services
const getCustomerById = useGetCustomerById({
   id: routeId,
   enabled: computed(() => !!routeId.value),
   onSuccess: (item) => {
      customer.value = { ...item };
   }
});

const getSaleAll = useGetSaleAll({
   enabled: computed(() => !!routeId.value)
});

const getInstallmentAll = useGetInstallmentAll({
   enabled: computed(() => !!routeId.value)
});

// loading states
const isLoading = computed(() => getCustomerById.isLoading.value);
const isError = computed(() => getCustomerById.isError.value);
const salesLoading = computed(() => getSaleAll.isLoading.value);
const installmentsLoading = computed(() => getInstallmentAll.isLoading.value);

// filtered data
const customerSales = computed(() => {
   if (!getSaleAll.data.value?.data || !routeId.value) return [];
   return getSaleAll.data.value.data.filter((sale: ISale) =>
      sale.customer_id === parseInt(routeId.value)
   );
});

const customerInstallments = computed(() => {
   if (!getInstallmentAll.data.value?.data || !customerSales.value.length) return [];
   const saleIds = customerSales.value.map(sale => sale.id);
   return getInstallmentAll.data.value.data.filter((installment: IInstallment) =>
      saleIds.includes(installment.sale_id)
   );
});

// table headers
const salesHeaders = computed(() => [
   { title: t("app.product"), key: "product", sortable: true },
   { title: t("app.price"), key: "price", sortable: true },
   { title: t("app.installment"), key: "installment", sortable: true },
   { title: t("app.installmentType"), key: "installment_type", sortable: true },
   { title: t("app.createdAt"), key: "created_at", sortable: true },
   { title: t("app.notes"), key: "notes", sortable: false }
]);

const installmentsHeaders = computed(() => [
   { title: t("app.product"), key: "sale", sortable: false },
   { title: t("app.amount"), key: "amount", sortable: true },
   { title: t("app.payment"), key: "payment", sortable: true },
   { title: t("app.status"), key: "status", sortable: true },
   { title: t("app.dueDate"), key: "due_at", sortable: true },
   { title: t("app.paidDate"), key: "paid_at", sortable: true }
]);

// helper functions
const formatCurrency = (value: string | number) => {
   const num = typeof value === 'string' ? parseFloat(value) : value;
   return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY'
   }).format(num || 0);
};

const formatDate = (dateString: string) => {
   if (!dateString) return '-';
   return new Date(dateString).toLocaleDateString('tr-TR');
};

const getInstallmentTypeColor = (type: string) => {
   switch (type?.toLowerCase()) {
      case 'nakit':
      case 'cash':
         return 'success';
      case 'kredi kartı':
      case 'credit card':
         return 'primary';
      case 'taksit':
      case 'installment':
         return 'warning';
      default:
         return 'default';
   }
};

const getInstallmentStatusColor = (status: string) => {
   switch (status?.toLowerCase()) {
      case 'paid':
      case 'ödendi':
         return 'success';
      case 'pending':
      case 'bekliyor':
         return 'warning';
      case 'overdue':
      case 'gecikmiş':
         return 'error';
      default:
         return 'default';
   }
};

const getInstallmentStatusText = (status: string) => {
   switch (status?.toLowerCase()) {
      case 'paid':
         return t('app.paid');
      case 'pending':
         return t('app.pending');
      case 'overdue':
         return t('app.overdue');
      default:
         return status;
   }
};
</script>