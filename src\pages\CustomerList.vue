<template>
   <Container v-bind:loading="isLoading">
      <Card v-bind:loading="isLoading">
         <template v-slot:header>
            <v-card-title class="flex-center p-0 text-base">{{ t("app.customerList") }}</v-card-title>
            <ActionButton
               v-bind:disabled="isLoading"
               :to="{ name: 'customerDetail', params: { id: 'create' } }">
               {{ t("app.add") }}
            </ActionButton>
         </template>

         <template v-slot:extension>
            <SearchInput v-model:search="filter" />
         </template>

         <DataTable
            v-model="selected"
            v-bind:filter="filter"
            v-bind:headers="headers"
            v-bind:items="data"
            v-bind:row-click="(item: ICustomer) => $router.push({ name: 'customerHistory', params: { id: item.id } })">
            <template v-slot:item.is_active="{ value }: { value: boolean }">
               <v-chip v-bind:color="value ? 'success' : undefined">
                  {{ value ? t("app.active") : t("app.passive") }}
               </v-chip>
            </template>
            <template v-slot:item.is_risky="{ value }: { value: boolean }">
               <v-chip v-bind:color="value ? 'error' : 'success'">
                  {{ value ? t("app.risky") : t("app.safe") }}
               </v-chip>
            </template>
            <template v-slot:item.current_debit="{ value }: { value: number }">
               <span v-bind:class="{ 'text-error': value > 0 }">
                  {{ formatMoney(value.toString()) }} ₺
               </span>
            </template>
            <template v-slot:item.actions="{ item }: { item: ICustomer }">
               <div class="table-action text-right opacity-0 transition-opacity [tr:hover_.table-action]:!opacity-100">
                  <v-btn
                     v-ripple.stop
                     class="opacity-50 hover:!opacity-100"
                     icon="$edit"
                     @click="$router.push({ name: 'customerDetail', params: { id: item.id } })" />
                  <v-btn
                     v-ripple.stop
                     class="opacity-50 hover:!opacity-100"
                     icon="$accountProfile"
                     @click="$router.push({ name: 'customerDetail', params: { id: item.id } })" />
               </div>
            </template>
         </DataTable>
      </Card>
   </Container>
</template>

<script lang="ts" setup>
import ActionButton from "@/components/Button/ActionButton.vue";
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import SearchInput from "@/components/Input/SearchInput.vue";
import DataTable from "@/components/Table/DataTable.vue";
import { ICustomer, useGetCustomerAll } from "../services/CustomerService";

const { t } = useI18n();

const filter = ref("");
const selected = ref([]);
const headers = computed((): THeader<ICustomer>[] => [
   { title: t("app.name"), key: "name", width: "150" },
   { title: t("app.surname"), key: "surname", width: "150" },
   { title: t("app.phone"), key: "phone", width: "150" },
   { title: t("app.email"), key: "email", width: "200" },
   { title: t("app.currentDebit"), key: "current_debit", width: "150" },
   { title: t("app.status"), key: "is_active", width: "100" },
   { title: t("app.riskStatus"), key: "is_risky", width: "100" },
   { title: t("app.actions"), key: "actions", width: "100" }
]);

const { data, isLoading } = useGetCustomerAll();
</script>