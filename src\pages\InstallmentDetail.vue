<template>
   <Container
      v-bind:error="isError"
      v-bind:form="formHandler"
      v-bind:loading="isLoading">
      <Card v-bind:loading="isLoading || isPending">
         <template v-slot:header>
            <v-card-title class="flex-center p-0 text-base">{{ title }}</v-card-title>
            <v-spacer />
            <ActionButton
               v-if="installment.id"
               v-bind:disabled="isLoading || isPending"
               color="error"
               prepend-icon="$trash"
               @click="removeHandler">
               {{ t("app.delete") }}
            </ActionButton>
            <ActionButton
               v-if="!isSuccess"
               v-bind:disabled="isLoading || isPending"
               type="submit"
               prepend-icon="$save">
               {{ isCreate ? t("app.save") : t("app.update") }}
            </ActionButton>
         </template>

         <template v-slot:extension>
            <v-card-title class="text-base">{{ t("app.basicInfo") }}</v-card-title>
         </template>

         <!-- Customer Information -->
         <v-card-text v-if="installmentCustomer && !isCreate">
            <v-card-title class="text-base mb-4">{{ t("app.customerInfo") }}</v-card-title>
            <v-row no-gutters>
               <v-col md="4">
                  <v-list-subheader>{{ t("app.customerName") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     :model-value="`${installmentCustomer.name} ${installmentCustomer.surname}`"
                     readonly
                     variant="outlined"
                     density="compact" />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.email") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     :model-value="installmentCustomer.email"
                     readonly
                     variant="outlined"
                     density="compact" />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.phone") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     :model-value="installmentCustomer.phone"
                     readonly
                     variant="outlined"
                     density="compact" />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.currentDebit") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     :model-value="formatMoney(installmentCustomer.current_debit)"
                     readonly
                     variant="outlined"
                     density="compact" />
               </v-col>
            </v-row>
         </v-card-text>

         <!-- Sale Information -->
         <v-card-text v-if="installmentSale && !isCreate">
            <v-card-title class="text-base mb-4">{{ t("app.saleInfo") }}</v-card-title>
            <v-row no-gutters>
               <v-col md="4">
                  <v-list-subheader>{{ t("app.product") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     :model-value="installmentSale.product"
                     readonly
                     variant="outlined"
                     density="compact"
                     disabled />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.price") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     :model-value="formatMoney(installmentSale.price)"
                     readonly
                     variant="outlined"
                     density="compact" />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.installmentCount") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     :model-value="installmentSale.installment"
                     readonly
                     variant="outlined"
                     density="compact" />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.installmentType") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     :model-value="t(`app.${installmentSale.installment_type}`)"
                     readonly
                     variant="outlined"
                     density="compact" />
               </v-col>
            </v-row>
         </v-card-text>

         <v-card-text>
            <v-row no-gutters>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.amount") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <NumberInput
                     v-model="installment.amount"
                     v-bind:rules="[appRules.required()]"
                     v-bind:step="0.01" />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.payment") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <NumberInput
                     v-model="installment.payment"
                     v-bind:rules="[appRules.required()]"
                     v-bind:step="0.01" />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.status") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <SelectInput
                     v-model="installment.status"
                     v-bind:items="statusOptions"
                     v-bind:rules="[appRules.required()]"
                     item-value="value"
                     item-title="title" />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.dueDate") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     v-model="installment.due_at"
                     type="datetime-local"
                     v-bind:rules="[appRules.required()]" />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.paidDate") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     v-model="installment.paid_at"
                     type="datetime-local" />
               </v-col>
            </v-row>
         </v-card-text>
      </Card>
   </Container>
</template>

<script lang="ts" setup>
import ActionButton from "@/components/Button/ActionButton.vue";
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import NumberInput from "@/components/Input/NumberInput.vue";
import SelectInput from "@/components/Input/SelectInput.vue";
import { formatDecimal, formatMoney } from "@/utils/helper";
import { appRules } from "@/utils/rules";
import { IInstallment, IInstallmentStore, useCreateInstallment, useDeleteInstallment, useGetInstallmentById, useUpdateInstallment, useGetInstallmentCustomer, useGetInstallmentSale } from "../services/InstallmentService";

const { t } = useI18n();
const route = useRoute() as TRoute;
const router = useRouter();
const appStore = useAppStore();
const snackbarStore = useSnackbarStore();
const confirmStore = useConfirmStore();

// installment
const installment = ref({
   status: "pending"
} as IInstallment);
const routeId = computed(() => route.params.id);
const isCreate = computed(() => routeId.value === "create");
const isEnabled = computed(() => !!routeId.value && !isCreate.value);
const title = computed(() => (isCreate.value ? t("app.createInstallment") : t("app.installmentDetail")));

// status options
const statusOptions = computed(() => [
   { value: "pending", title: t("app.pending") },
   { value: "paid", title: t("app.paid") },
   { value: "overdue", title: t("app.overdue") }
]);

// set breadcrumb
appStore.setBreadcrumb("InstallmentDetail", title);

// services
const getInstallmentById = useGetInstallmentById({
   id: routeId,
   enabled: isEnabled,
   onSuccess: (item) => {
      installment.value = { ...item };
      installment.value.amount = formatMoney(installment.value.amount);
      installment.value.payment = formatMoney(installment.value.payment);

      // Format dates for datetime-local input
      if (installment.value.due_at) {
         installment.value.due_at = new Date(installment.value.due_at).toISOString().slice(0, 16);
      }
      if (installment.value.paid_at) {
         installment.value.paid_at = new Date(installment.value.paid_at).toISOString().slice(0, 16);
      }
   }
});
const updateInstallment = useUpdateInstallment();
const createInstallment = useCreateInstallment();
const deleteInstallment = useDeleteInstallment();

// relation services
const { data: installmentCustomer } = useGetInstallmentCustomer({
   id: routeId,
   enabled: isEnabled
});
const { data: installmentSale } = useGetInstallmentSale({
   id: routeId,
   enabled: isEnabled
});

// loading
const isLoading = computed(() => getInstallmentById.isLoading.value);
const isPending = computed(() => createInstallment.isPending.value || updateInstallment.isPending.value);
const isError = computed(() => getInstallmentById.isError.value);
const isSuccess = computed(() => createInstallment.isSuccess.value);

// handlers
const formHandler = async () => {
   const payload: IInstallmentStore = {
      sale_id: installment.value.sale?.id || installment.value.sale_id,
      amount: formatDecimal(installment.value.amount || "0"),
      payment: formatDecimal(installment.value.payment || "0"),
      status: installment.value.status,
      due_at: installment.value.due_at ? new Date(installment.value.due_at).toISOString().replace('T', ' ').slice(0, 19) : undefined,
      paid_at: installment.value.paid_at ? new Date(installment.value.paid_at).toISOString().replace('T', ' ').slice(0, 19) : undefined
   };

   try {
      if (isCreate.value) {
         await createInstallment.mutateAsync(payload);
         snackbarStore.add({ text: t("app.recordCreated") });
      } else {
         await updateInstallment.mutateAsync({ id: installment.value.id, ...payload });
         snackbarStore.add({ text: t("app.recordUpdated") });
      }
   } catch (error: any) {
      snackbarStore.add({ text: error.data?.error || t("app.recordFailed"), color: "error" });
   }
};

const removeHandler = async () => {
   try {
      const confirm = await confirmStore.open({
         title: t("app.confirmTitle"),
         message: t("app.deleteInstallment")
      });

      if (confirm) {
         await deleteInstallment.mutateAsync(installment.value.id);
         snackbarStore.add({ text: t("app.recordDeleted") });
         router.push({ name: "installmentList" });
      }
   } catch {
      snackbarStore.add({ text: t("app.recordFailed"), color: "error" });
   } finally {
      confirmStore.close();
   }
};
</script>