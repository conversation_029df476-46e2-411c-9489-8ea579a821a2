<template>
   <Container v-bind:loading="isLoading">
      <Card v-bind:loading="isLoading">
         <template v-slot:header>
            <v-card-title class="flex-center p-0 text-base">{{ t("app.installmentList") }}</v-card-title>
            <ActionButton
               v-bind:disabled="isLoading"
               :to="{ name: 'installmentDetail', params: { id: 'create' } }">
               {{ t("app.add") }}
            </ActionButton>
         </template>

         <template v-slot:extension>
            <SearchInput v-model:search="filter" />
         </template>

         <DataTable
            v-model="selected"
            v-bind:filter="filter"
            v-bind:headers="headers"
            v-bind:items="data"
            v-bind:row-click="(item: IInstallment) => $router.push({ name: 'installmentDetail', params: { id: item.id } })">
            <template v-slot:item.customer="{ item }: { item: IInstallment }">
               {{ item.sale?.customer ? `${item.sale?.customer?.name} ${item.sale?.customer?.surname}` : "-" }}
            </template>
            <template v-slot:item.sale="{ item }: { item: IInstallment }">
               {{ item.sale?.product || "-" }}
            </template>
            <template v-slot:item.amount="{ value }: { value: string }">
               {{ value }} ₺
            </template>
            <template v-slot:item.payment="{ value }: { value: string }">
               {{ value }} ₺
            </template>
            <template v-slot:item.status="{ value }: { value: string }">
               <v-chip size="small" v-bind:color="value === 'paid' ? 'success' : value === 'pending' ? 'warning' : 'error'">
                  {{ value === 'paid' ? t("app.paid") : value === 'pending' ? t("app.pending") : value === 'overdue' ? t("app.overdue") : value }}
               </v-chip>
            </template>
            <template v-slot:item.due_at="{ value }: { value: string }">
               {{ value ? new Date(value).toLocaleDateString('tr-TR') : "-" }}
            </template>
            <template v-slot:item.paid_at="{ value }: { value: string }">
               {{ value ? new Date(value).toLocaleDateString('tr-TR') : "-" }}
            </template>
            <template v-slot:item.actions="{ item }: { item: IInstallment }">
               <div class="table-action text-right opacity-0 transition-opacity [tr:hover_.table-action]:!opacity-100">
                  <v-btn
                     v-ripple.stop
                     class="opacity-50 hover:!opacity-100"
                     icon="$edit"
                     @click="$router.push({ name: 'installmentDetail', params: { id: item.id } })" />
                  <v-btn
                     v-ripple.stop
                     class="opacity-50 hover:!opacity-100"
                     icon="$accountProfile" />
               </div>
            </template>
         </DataTable>
      </Card>
   </Container>
</template>

<script lang="ts" setup>
import ActionButton from "@/components/Button/ActionButton.vue";
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import SearchInput from "@/components/Input/SearchInput.vue";
import DataTable from "@/components/Table/DataTable.vue";
import { IInstallment, useGetInstallmentAll } from "../services/InstallmentService";

const { t } = useI18n();
const queryClient = useQueryClient();

// Sayfa yüklendiğinde cache'i temizle
onMounted(() => {
   queryClient.invalidateQueries({ queryKey: ["installment"] });
});

const filter = ref("");
const selected = ref([]);
const headers = computed((): THeader<IInstallment>[] => [
   { title: t("app.customer"), key: "customer" as keyof IInstallment, width: "200" },
   { title: t("app.product"), key: "sale", width: "200" },
   { title: t("app.amount"), key: "amount", width: "120" },
   { title: t("app.payment"), key: "payment", width: "120" },
   { title: t("app.status"), key: "status", width: "120" },
   { title: t("app.dueDate"), key: "due_at", width: "120" },
   { title: t("app.paidDate"), key: "paid_at", width: "120" },
   { title: t("app.actions"), key: "actions", width: "100", sortable: false }
]);

const { data, isLoading } = useGetInstallmentAll();

</script>