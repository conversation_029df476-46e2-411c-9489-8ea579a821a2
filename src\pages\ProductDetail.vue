<template>
   <Container
      v-bind:error="isError"
      v-bind:form="formHandler"
      v-bind:loading="isLoading">
      <Card v-bind:loading="isLoading || isPending">
         <template v-slot:header>
            <v-card-title class="flex-center p-0 text-base">{{ title }}</v-card-title>
            <v-spacer />
            <ActionButton
               v-if="product.id"
               v-bind:disabled="isLoading || isPending"
               color="error"
               prepend-icon="$trash"
               @click="removeHandler">
               {{ t("app.delete") }}
            </ActionButton>
            <ActionButton
               v-if="!isSuccess"
               v-bind:disabled="isLoading || isPending"
               type="submit"
               prepend-icon="$save">
               {{ isCreate ? t("app.save") : t("app.update") }}
            </ActionButton>
         </template>

         <template v-slot:extension>
            <v-card-title class="text-base">{{ t("app.basicInfo") }}</v-card-title>
            <LanguageTab
               v-model="language"
               v-bind:loading="isLoading" />
         </template>

         <v-card-text>
            <v-row no-gutters>
               <v-col md="4">
                  <v-list-subheader>{{ t("app.code") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     v-model="product.code"
                     v-bind:rules="[appRules.required()]" />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.title") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     v-model="product.title"
                     v-bind:rules="[appRules.required()]">
                     <template
                        v-if="language !== 1"
                        v-slot:append-inner>
                        <TranslateButton v-model="product.title" />
                     </template>
                  </v-text-field>
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.description") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-textarea
                     v-model="product.content"
                     v-bind:rules="[appRules.required()]"
                     auto-grow
                     no-resize>
                     <template
                        v-if="language !== 1"
                        v-slot:append-inner>
                        <TranslateButton v-model="product.content" />
                     </template>
                  </v-textarea>
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.price") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <NumberInput
                     v-model="product.price"
                     v-bind:step="1" />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.stock") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field v-model.number="product.stock" />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.status") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-switch
                     v-model="product.is_active"
                     v-bind:false-value="0"
                     v-bind:ripple="false"
                     v-bind:true-value="1"
                     color="primary"
                     density="compact">
                     <template v-slot:label>
                        <div class="text-sm">{{ product.is_active ? t("app.active") : t("app.passive") }}</div>
                     </template>
                  </v-switch>
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.category") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <SelectInput
                     v-model="product.category"
                     v-bind:items="categoryAll"
                     v-bind:loading="categoryLoading"
                     item-value="id" />
               </v-col>
            </v-row>
         </v-card-text>
      </Card>
   </Container>
</template>

<script lang="ts" setup>
import ActionButton from "@/components/Button/ActionButton.vue";
import TranslateButton from "@/components/Button/TranslateButton.vue";
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import NumberInput from "@/components/Input/NumberInput.vue";
import SelectInput from "@/components/Input/SelectInput.vue";
import LanguageTab from "@/components/Tab/LanguageTab.vue";
import { formatDecimal, formatMoney } from "@/utils/helper";
import { appRules } from "@/utils/rules";
import { useGetCategoryAll } from "../services/CategoryService";
import { IProduct, IProductStore, useCreateProduct, useDeleteProduct, useGetProductById, useUpdateProduct } from "../services/ProductService";

const { t } = useI18n();
const route = useRoute() as TRoute;
const appStore = useAppStore();
const snackbarStore = useSnackbarStore();
const confirmStore = useConfirmStore();

// product
const product = ref({
   is_active: 1,
   sort_order: 1,
   stock: 0,
   is_temp: 0
} as IProduct);
const routeId = computed(() => route.params.id);
const isCreate = computed(() => routeId.value === "create");
const isEnabled = computed(() => !!routeId.value && !isCreate.value);
const language = ref(1);
const title = computed(() => (isCreate.value ? t("app.createProduct") : t("app.productDetail")));

// set breadcrumb
appStore.setBreadcrumb("ProductDetail", title);

// services
const getProductById = useGetProductById({
   id: routeId,
   enabled: isEnabled,
   language: language,
   onSuccess: (item) => {
      product.value = { ...item };
      product.value.price = formatMoney(product.value.price);
   }
});
const updateProduct = useUpdateProduct();
const createProduct = useCreateProduct();
const deleteProduct = useDeleteProduct();

// relation services
const { data: categoryAll, isLoading: categoryLoading } = useGetCategoryAll();

// loading
const isLoading = computed(() => getProductById.isLoading.value);
const isPending = computed(() => createProduct.isPending.value || updateProduct.isPending.value);
const isError = computed(() => getProductById.isError.value);
const isSuccess = computed(() => createProduct.isSuccess.value);

// handlers
const formHandler = async () => {
   const payload: IProductStore = {
      code: product.value.code,
      title: product.value.title,
      content: product.value.content,
      is_active: product.value.is_active,
      sort_order: product.value.sort_order,
      category_id: product.value.category?.id,
      is_temp: product.value.is_temp,
      price: formatDecimal(product.value.price || "0"),
      stock: product.value.stock
   };

   try {
      if (isCreate.value) {
         await createProduct.mutateAsync(payload);
         snackbarStore.add({ text: t("app.recordCreated") });
      } else {
         await updateProduct.mutateAsync({ id: product.value.id, ...payload });
         snackbarStore.add({ text: t("app.recordUpdated") });
      }
   } catch (error: any) {
      snackbarStore.add({ text: error.data?.error || t("app.recordFailed"), color: "error" });
   }
};
const removeHandler = async () => {
   try {
      const confirm = await confirmStore.open({
         title: t("app.confirmTitle"),
         message: t("app.deleteProduct")
      });

      if (confirm) {
         await deleteProduct.mutateAsync(product.value.id);
         snackbarStore.add({ text: t("app.recordDeleted") });
         router.push({ name: "productList" });
      }
   } catch {
      snackbarStore.add({ text: t("app.recordFailed"), color: "error" });
   } finally {
      confirmStore.close();
   }
};
</script>
