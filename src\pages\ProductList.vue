<template>
   <Container v-bind:loading="isLoading">
      <Card v-bind:loading="isLoading">
         <template v-slot:header>
            <v-card-title class="flex-center p-0 text-base">{{ t("app.productList") }}</v-card-title>
            <ActionButton
               v-bind:disabled="isLoading"
               :to="{ name: 'productDetail', params: { id: 'create' } }">
               {{ t("app.add") }}
            </ActionButton>
         </template>

         <template v-slot:extension>
            <SearchInput v-model:search="filter" />
         </template>

         <DataTable
            v-model="selected"
            v-bind:filter="filter"
            v-bind:headers="headers"
            v-bind:items="data"
            v-bind:row-click="(item: IProduct) => $router.push({ name: 'productDetail', params: { id: item.id } })">
            <template v-slot:item.is_active="{ value }: { value: boolean }">
               <v-chip v-bind:color="value ? 'success' : undefined">
                  {{ value ? t("app.active") : t("app.passive") }}
               </v-chip>
            </template>
            <template v-slot:item.category="{ item }: { item: IProduct }">
               {{ item.category?.title || "-" }}
            </template>
            <template v-slot:item.actions="{ item }: { item: IProduct }">
               <div class="table-action text-right opacity-0 transition-opacity [tr:hover_.table-action]:!opacity-100">
                  <v-btn
                     v-ripple.stop
                     class="opacity-50 hover:!opacity-100"
                     icon="$edit"
                     @click="console.log(item)" />
                  <v-btn
                     v-ripple.stop
                     class="opacity-50 hover:!opacity-100"
                     icon="$accountProfile" />
               </div>
            </template>
         </DataTable>
      </Card>
   </Container>
</template>

<script lang="ts" setup>
import ActionButton from "@/components/Button/ActionButton.vue";
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import SearchInput from "@/components/Input/SearchInput.vue";
import DataTable from "@/components/Table/DataTable.vue";
import { IProduct, useGetProductAll } from "../services/ProductService";

const { t } = useI18n();

const filter = ref("");
const selected = ref([]);
const headers = computed((): THeader<IProduct>[] => [
   { title: t("app.code"), key: "code", width: "100" },
   { title: t("app.title"), key: "title" },
   { title: t("app.status"), key: "is_active", width: "150" },
   { title: t("app.category"), key: "category", width: "350" }
]);

const { data, isLoading } = useGetProductAll();

</script>
