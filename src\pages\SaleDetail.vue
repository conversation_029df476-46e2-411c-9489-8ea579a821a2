<template>
   <Container
      v-bind:error="isError"
      v-bind:form="formHandler"
      v-bind:loading="isLoading">
      <Card v-bind:loading="isLoading || isPending">
         <template v-slot:header>
            <v-card-title class="flex-center p-0 text-base">{{ title }}</v-card-title>
            <v-spacer />
            <ActionButton v-if="sale.id" v-bind:disabled="isLoading || isPending" color="error" prepend-icon="$trash" @click="removeHandler">
               {{ t("app.delete") }}
            </ActionButton>
            <ActionButton v-if="!isSuccess" v-bind:disabled="isLoading || isPending" type="submit" prepend-icon="$save">
               {{ sale.id ? t("app.update") : t("app.save") }}
            </ActionButton>
         </template>

         <template v-slot:extension>
            <v-card-title class="text-base">{{ t("app.basicInfo") }}</v-card-title>
         </template>

         <v-card-text>
            <v-row no-gutters>
               <v-col md="4">
                  <v-list-subheader>{{ t("app.customer") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <SelectInput
                     v-model="sale.customer"
                     v-bind:items="customerAll"
                     v-bind:loading="customerLoading"
                     v-bind:rules="[appRules.required()]"
                     item-value="id"
                     item-title="name"
                     return-object
                     v-bind:model-value="sale.customer || customerAll?.find(c => c.id === sale.customer_id)">
                     <template v-slot:item="{ item }">
                        <v-list-item :title="`${item.raw.name} ${item.raw.surname}`" />
                     </template>
                     <template v-slot:selection="{ item }">
                        {{ `${item.raw.name} ${item.raw.surname}` }}
                     </template>
                  </SelectInput>
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.product") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     v-model="sale.product"
                     v-bind:rules="[appRules.required()]" />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.price") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <NumberInput
                     v-model="sale.price"
                     v-bind:rules="[appRules.required()]"
                     v-bind:step="0.01" />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.installment") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     v-model.number="sale.installment"
                     v-bind:rules="[appRules.required()]"
                     type="number"
                     min="1" />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.installmentType") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-select
                     v-model="sale.installment_type"
                     v-bind:items="installmentTypes"
                     v-bind:rules="[appRules.required()]" />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.notes") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-textarea
                     v-model="sale.notes"
                     auto-grow
                     no-resize />
               </v-col>
            </v-row>
         </v-card-text>
      </Card>
   </Container>
</template>

<script lang="ts" setup>
import ActionButton from "@/components/Button/ActionButton.vue";
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import NumberInput from "@/components/Input/NumberInput.vue";
import SelectInput from "@/components/Input/SelectInput.vue";
import { appRules } from "@/utils/rules";
import { useGetCustomerAll } from "../services/CustomerService";
import { ISale, ISaleStore, useCreateSale, useDeleteSale, useGetSaleById, useUpdateSale } from "../services/SaleService";

const { t } = useI18n();
const route = useRoute() as TRoute;
const appStore = useAppStore();
const snackbarStore = useSnackbarStore();
const confirmStore = useConfirmStore();

// sale
const sale = ref({
   installment: 1,
   installment_type: 'monthly'
} as ISale);
const routeId = computed(() => route.params.id);
const isCreate = computed(() => routeId.value === "create");
const isEnabled = computed(() => !!routeId.value && !isCreate.value);
const title = computed(() => (isCreate.value ? t("app.createSale") : t("app.saleDetail")));

// installment types
const installmentTypes = computed(() => [
   { title: t("app.monthly"), value: "monthly" },
   { title: t("app.weekly"), value: "weekly" },
   { title: t("app.daily"), value: "daily" }
]);

// set breadcrumb
appStore.setBreadcrumb("SaleDetail", title);

// services
const getSaleById = useGetSaleById({
   id: routeId,
   enabled: isEnabled,
   onSuccess: (item) => {
      sale.value = { ...item };
      sale.value.price = formatMoney(sale.value.price);
   }
});
const updateSale = useUpdateSale();
const createSale = useCreateSale();
const deleteSale = useDeleteSale();

// relation services
const { data: customerAll, isLoading: customerLoading } = useGetCustomerAll();

// loading
const isLoading = computed(() => getSaleById.isLoading.value);
const isPending = computed(() => createSale.isPending.value || updateSale.isPending.value);
const isError = computed(() => getSaleById.isError.value);
const isSuccess = computed(() => createSale.isSuccess.value);

// handlers
const formHandler = async () => {
   const payload: ISaleStore = {
      customer_id: sale.value.customer?.id || sale.value.customer_id,
      product: sale.value.product,
      price: formatDecimal(sale.value.price),
      installment: sale.value.installment,
      installment_type: sale.value.installment_type,
      notes: sale.value.notes
   };

   try {
      if (isCreate.value) {
         await createSale.mutateAsync(payload);
         snackbarStore.add({ text: t("app.recordCreated") });
      } else {
         await updateSale.mutateAsync({ id: sale.value.id, ...payload });
         snackbarStore.add({ text: t("app.recordUpdated") });
      }
   } catch (error) {
      snackbarStore.add({ text: t("app.recordFailed"), color: "error" });
   }
};

const removeHandler = async () => {
   try {
      const confirm = await confirmStore.open({
         title: t("app.confirmTitle"),
         message: t("app.deleteSale")
      });

      if (confirm) {
         await deleteSale.mutateAsync(sale.value.id);
         snackbarStore.add({ text: t("app.recordDeleted") });
         router.push({ name: "saleList" });
      }
   } catch {
      snackbarStore.add({ text: t("app.recordFailed"), color: "error" });
   } finally {
      confirmStore.close();
   }
};
</script>