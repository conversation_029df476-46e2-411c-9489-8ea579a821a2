<template>
   <Container v-bind:loading="isLoading">
      <Card v-bind:loading="isLoading">
         <template v-slot:header>
            <v-card-title class="flex-center p-0 text-base">{{ t("app.saleList") }}</v-card-title>
            <ActionButton
               v-bind:disabled="isLoading"
               :to="{ name: 'saleDetail', params: { id: 'create' } }">
               {{ t("app.add") }}
            </ActionButton>
         </template>

         <template v-slot:extension>
            <SearchInput v-model:search="filter" />
         </template>

         <DataTable
            v-model="selected"
            v-bind:filter="filter"
            v-bind:headers="headers"
            v-bind:items="data"
            v-bind:row-click="(item: ISale) => $router.push({ name: 'saleDetail', params: { id: item.id } })">
            <template v-slot:item.customer="{ item }: { item: ISale }">
               {{ item.customer ? `${item.customer.name} ${item.customer.surname}` : "-" }}
            </template>
            <template v-slot:item.price="{ value }: { value: string }">
               {{ value }} ₺
            </template>
            <template v-slot:item.installment_type="{ value }: { value: string }">
               <v-chip size="small" color="primary">
                  {{ value === 'monthly' ? t("app.monthly") : value === 'weekly' ? t("app.weekly") : value }}
               </v-chip>
            </template>
            <template v-slot:item.actions="{ item }: { item: ISale }">
               <div class="table-action text-right opacity-0 transition-opacity [tr:hover_.table-action]:!opacity-100">
                  <v-btn
                     v-ripple.stop
                     class="opacity-50 hover:!opacity-100"
                     icon="$edit"
                     @click="$router.push({ name: 'saleDetail', params: { id: item.id } })" />
                  <v-btn
                     v-ripple.stop
                     class="opacity-50 hover:!opacity-100"
                     icon="$accountProfile" />
               </div>
            </template>
         </DataTable>
      </Card>
   </Container>
</template>

<script lang="ts" setup>
import ActionButton from "@/components/Button/ActionButton.vue";
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import SearchInput from "@/components/Input/SearchInput.vue";
import DataTable from "@/components/Table/DataTable.vue";
import { ISale, useGetSaleAll } from "../services/SaleService";

const { t } = useI18n();
const queryClient = useQueryClient();

// Sayfa yüklendiğinde cache'i temizle
onMounted(() => {
   queryClient.invalidateQueries({ queryKey: ["sale"] });
});

const filter = ref("");
const selected = ref([]);
const headers = computed((): THeader<ISale>[] => [
   { title: t("app.customer"), key: "customer", width: "200" },
   { title: t("app.product"), key: "product" },
   { title: t("app.price"), key: "price", width: "120" },
   { title: t("app.installment"), key: "installment", width: "100" },
   { title: t("app.installmentType"), key: "installment_type", width: "150" },
   { title: t("app.actions"), key: "actions", width: "100", sortable: false }
]);

const { data, isLoading } = useGetSaleAll();

</script>