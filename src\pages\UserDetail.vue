<template>
   <Container
      v-bind:error="isError"
      v-bind:form="formHandler"
      v-bind:loading="isLoading">
      <Card v-bind:loading="isLoading || isPending">
         <template v-slot:header>
            <v-card-title class="flex-center p-0 text-base">{{ title }}</v-card-title>
            <v-spacer />
            <ActionButton
               v-if="user.id"
               v-bind:disabled="isLoading || isPending"
               color="error"
               prepend-icon="$trash"
               @click="removeHandler">
               {{ t("app.delete") }}
            </ActionButton>
            <ActionButton
               v-if="!isSuccess"
               v-bind:disabled="isLoading || isPending"
               type="submit"
               prepend-icon="$save">
               {{ isCreate ? t("app.save") : t("app.update") }}
            </ActionButton>
         </template>

         <template v-slot:extension>
            <v-card-title class="text-base">{{ t("app.basicInfo") }}</v-card-title>
         </template>

         <v-card-text>
            <v-row no-gutters>
               <v-col md="4">
                  <v-list-subheader>{{ t("app.username") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     v-model="user.username"
                     v-bind:rules="[appRules.required()]" />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.name") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     v-model="user.name"
                     v-bind:rules="[appRules.required()]" />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.surname") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     v-model="user.surname"
                     v-bind:rules="[appRules.required()]" />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.email") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     v-model="user.email"
                     v-bind:rules="[appRules.email()]"
                     type="email" />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.phone") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     v-model="user.phone"
                     type="tel" />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.password") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-text-field
                     v-model="user.password"
                     v-bind:rules="[appRules.required(), appRules.minlen(6)]"
                     type="password" />
               </v-col>

               <v-col md="4">
                  <v-list-subheader>{{ t("app.role") }}</v-list-subheader>
               </v-col>
               <v-col md="8">
                  <v-select
                     v-model="user.role"
                     v-bind:items="roleOptions"
                     v-bind:rules="[appRules.required()]"
                     item-title="text"
                     item-value="value" />
               </v-col>
            </v-row>
         </v-card-text>
      </Card>
   </Container>
</template>

<script lang="ts" setup>
import ActionButton from "@/components/Button/ActionButton.vue";
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import { appRules } from "@/utils/rules";
import { IUser, IUserStore, useCreateUser, useDeleteUser, useGetUserById, useUpdateUser } from "../services/UserService";

const { t } = useI18n();
const route = useRoute() as TRoute;
const router = useRouter();
const appStore = useAppStore();
const snackbarStore = useSnackbarStore();
const confirmStore = useConfirmStore();

// user
const user = ref({} as IUser);
const routeId = computed(() => route.params.id);
const isCreate = computed(() => routeId.value === "create");
const isEnabled = computed(() => !!routeId.value && !isCreate.value);
const title = computed(() => (isCreate.value ? t("app.createUser") : t("app.userDetail")));

// role options
const roleOptions = computed(() => [
   { text: t("app.admin"), value: "admin" },
   { text: t("app.user"), value: "user" },
   { text: t("app.manager"), value: "manager" }
]);

// set breadcrumb
appStore.setBreadcrumb("UserDetail", title);

// services
const getUserById = useGetUserById({
   id: routeId,
   enabled: isEnabled,
   onSuccess: (item) => {
      user.value = { ...item };
   }
});
const updateUser = useUpdateUser();
const createUser = useCreateUser();
const deleteUser = useDeleteUser();

// loading
const isLoading = computed(() => getUserById.isLoading.value);
const isPending = computed(() => createUser.isPending.value || updateUser.isPending.value);
const isError = computed(() => getUserById.isError.value);
const isSuccess = computed(() => createUser.isSuccess.value);

// handlers
const formHandler = async () => {
   const payload: IUserStore = {
      username: user.value.username,
      name: user.value.name,
      surname: user.value.surname,
      email: user.value.email,
      phone: user.value.phone,
      password: user.value.password,
      role: user.value.role
   };

   try {
      if (isCreate.value) {
         await createUser.mutateAsync(payload);
         snackbarStore.add({ text: t("app.recordCreated") });
      } else {
         await updateUser.mutateAsync({ id: user.value.id, ...payload });
         snackbarStore.add({ text: t("app.recordUpdated") });
      }
   } catch (error: any) {
      snackbarStore.add({ text: error.data?.error || t("app.recordFailed"), color: "error" });
   }
};

const removeHandler = async () => {
   try {
      const confirm = await confirmStore.open({
         title: t("app.confirmTitle"),
         message: t("app.deleteUser")
      });

      if (confirm) {
         await deleteUser.mutateAsync(user.value.id);
         snackbarStore.add({ text: t("app.recordDeleted") });
         router.push({ name: "userList" });
      }
   } catch {
      snackbarStore.add({ text: t("app.recordFailed"), color: "error" });
   } finally {
      confirmStore.close();
   }
};
</script>