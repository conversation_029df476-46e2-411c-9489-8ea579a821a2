<template>
   <Container v-bind:loading="isLoading">
      <Card v-bind:loading="isLoading">
         <template v-slot:header>
            <v-card-title class="flex-center p-0 text-base">{{ t("app.userList") }}</v-card-title>
            <ActionButton
               v-bind:disabled="isLoading"
               :to="{ name: 'userDetail', params: { id: 'create' } }">
               {{ t("app.add") }}
            </ActionButton>
         </template>

         <template v-slot:extension>
            <SearchInput v-model:search="filter" />
         </template>

         <DataTable
            v-model="selected"
            v-bind:filter="filter"
            v-bind:headers="headers"
            v-bind:items="data"
            v-bind:row-click="(item: IUser) => $router.push({ name: 'userDetail', params: { id: item.id } })">
            <template v-slot:item.role="{ value }: { value: string }">
               <v-chip v-bind:color="value === 'admin' ? 'primary' : 'secondary'">
                  {{ t(`app.role.${value}`) }}
               </v-chip>
            </template>
            <template v-slot:item.fullName="{ item }: { item: IUser }">
               {{ `${item.name} ${item.surname}` }}
            </template>
            <template v-slot:item.actions="{ item }: { item: IUser }">
               <div class="table-action text-right opacity-0 transition-opacity [tr:hover_.table-action]:!opacity-100">
                  <v-btn
                     v-ripple.stop
                     class="opacity-50 hover:!opacity-100"
                     icon="$edit"
                     @click="console.log(item)" />
                  <v-btn
                     v-ripple.stop
                     class="opacity-50 hover:!opacity-100"
                     icon="$accountProfile" />
               </div>
            </template>
         </DataTable>
      </Card>
   </Container>
</template>

<script lang="ts" setup>
import ActionButton from "@/components/Button/ActionButton.vue";
import Card from "@/components/Card/Card.vue";
import Container from "@/components/Container/Container.vue";
import SearchInput from "@/components/Input/SearchInput.vue";
import DataTable from "@/components/Table/DataTable.vue";
import { IUser, useGetUserAll } from "../services/UserService";

const { t } = useI18n();

const filter = ref("");
const selected = ref([]);
const headers = computed((): THeader<IUser>[] => [
   { title: t("app.username"), key: "username", width: "150" },
   { title: t("app.name"), key: "name", width: "200" },
   { title: t("app.surname"), key: "surname", width: "200" },
   { title: t("app.email"), key: "email", width: "200" },
   { title: t("app.phone"), key: "phone", width: "150" },
   { title: t("app.role"), key: "role", width: "120" }
]);

const { data, isLoading } = useGetUserAll();

</script>