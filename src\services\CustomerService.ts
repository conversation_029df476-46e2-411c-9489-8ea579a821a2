export interface ICustomer extends IDefaultFields {
   id: number;
   name: string;
   surname: string;
   email?: string;
   phone: string;
   tckn?: string;
   address?: string;
   current_debit: string;
   is_risky: number;
   is_active: number;
   notes?: string;
}

export interface ICustomerStore {
   id?: number;
   name: string;
   surname: string;
   email?: string;
   phone: string;
   tckn?: string;
   address?: string;
   current_debit?: number;
   is_risky?: number;
   is_active?: number;
   notes?: string;
}

export const useGetCustomerAll = (payload?: TQuery<ICustomer[]>) => {
   const options = computed(() => ({
      queryKey: ["customer", "customerAll"],
      queryFn: async () => {
         return (await appAxios.get("/customer/")).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useGetCustomerById = (payload?: { id?: MaybeRef<string> } & TQuery<ICustomer>) => {
   const options = computed(() => ({
      queryKey: ["customer", "customerById", payload?.id],
      queryFn: async ({ signal }: { signal: AbortSignal }) => {
         return (await appAxios.get(`/customer/${toValue(payload?.id)}`, { signal })).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useUpdateCustomer = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["customer", "updateCustomer"],
      mutationFn: async (data: ICustomerStore): Promise<TResponse<ICustomer>> => {
         return (await appAxios.put("/customer/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["customer"] });
      }
   });
};

export const useCreateCustomer = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["customer", "createCustomer"],
      mutationFn: async (data: ICustomerStore): Promise<TResponse<ICustomer>> => {
         return (await appAxios.post("/customer/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["customer"] });
      }
   });
};

export const useDeleteCustomer = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["customer", "deleteCustomer"],
      mutationFn: async (id: number): Promise<TResponse<boolean>> => {
         return (await appAxios.delete(`/customer/${id}`)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["customer"] });
      }
   });
};