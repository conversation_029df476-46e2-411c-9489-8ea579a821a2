export interface IInstallment extends IDefaultFields {
   id: number;
   sale_id: number;
   amount: string;
   payment: string;
   status: string;
   due_at?: string;
   paid_at?: string;
   sale?: { id: number; product: string; customer?: { name: string; surname: string } };
}

export interface IInstallmentStore {
   id?: number;
   sale_id: number;
   amount: number;
   payment: number;
   status: string;
   due_at?: string;
   paid_at?: string;
}

export const useGetInstallmentAll = (payload?: TQuery<IInstallment[]>) => {
   const options = computed(() => ({
      queryKey: ["installment", "installmentAll"],
      queryFn: async () => {
         const response = (await appAxios.get("/installment/")).data;
         // Her taksit için satış ve müşteri bilgilerini al
         if (response.data && Array.isArray(response.data)) {
            const installmentsWithDetails = await Promise.all(
               response.data.map(async (installment: IInstallment) => {
                  try {
                     // Satış bilgilerini çek
                     const saleResponse = await appAxios.get(`/installment/${installment.id}/sale`);
                     let saleData = saleResponse.data.data;

                     // Müşteri bilgilerini çek
                     try {
                        const customerResponse = await appAxios.get(`/installment/${installment.id}/customer`);
                        saleData = {
                           ...saleData,
                           customer: customerResponse.data.data
                        };
                     } catch (customerError) {
                        // Müşteri bilgisi yoksa devam et
                     }

                     return {
                        ...installment,
                        sale: saleData
                     };
                  } catch (error) {
                     return installment;
                  }
               })
            );
            return {
               ...response,
               data: installmentsWithDetails
            };
         }
         return response;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useGetInstallmentById = (payload?: { id?: MaybeRef<string> } & TQuery<IInstallment>) => {
   const options = computed(() => ({
      queryKey: ["installment", "installmentById", payload?.id, payload?.language],
      queryFn: async ({ signal }: { signal: AbortSignal }) => {
         return (await appAxios.get(`/installment/${toValue(payload?.id)}`, { signal, params: { lang_id: toValue(payload?.language) || 1 } })).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useUpdateInstallment = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["installment", "updateInstallment"],
      mutationFn: async (data: IInstallmentStore): Promise<TResponse<IInstallment>> => {
         return (await appAxios.put("/installment/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["installment"] });
      }
   });
};

export const useCreateInstallment = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["installment", "createInstallment"],
      mutationFn: async (data: IInstallmentStore): Promise<TResponse<IInstallment>> => {
         return (await appAxios.post("/installment/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["installment"] });
      }
   });
};

export const useDeleteInstallment = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["installment", "deleteInstallment"],
      mutationFn: async (id: number): Promise<TResponse<boolean>> => {
         return (await appAxios.delete(`/installment/${id}`)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["installment"] });
      }
   });
};

export const useGetInstallmentCustomer = (payload?: { id?: MaybeRef<string> } & TQuery<any>) => {
   const options = computed(() => ({
      queryKey: ["installment", "installmentCustomer", payload?.id],
      queryFn: async ({ signal }: { signal: AbortSignal }) => {
         return (await appAxios.get(`/installment/${toValue(payload?.id)}/customer`, { signal })).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useGetInstallmentSale = (payload?: { id?: MaybeRef<string> } & TQuery<any>) => {
   const options = computed(() => ({
      queryKey: ["installment", "installmentSale", payload?.id],
      queryFn: async ({ signal }: { signal: AbortSignal }) => {
         return (await appAxios.get(`/installment/${toValue(payload?.id)}/sale`, { signal })).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};