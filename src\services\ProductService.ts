export interface IProduct extends IDefaultFields {
   id: number;
   code: string;
   title: string;
   content?: string;
   price?: string;
   stock?: number;
   is_active: number;
   sort_order: number;
   is_temp: number;
   category?: { id: number; title: string };
}

export interface IProductStore {
   id?: number;
   code: string;
   title: string;
   content?: string;
   price?: number;
   stock?: number;
   is_temp?: number;
   is_active?: number;
   sort_order?: number;
   category_id?: number;
}

export const useGetProductAll = (payload?: TQuery<IProduct[]>) => {
   const options = computed(() => ({
      queryKey: ["product", "productAll"],
      queryFn: async () => {
         return (await appAxios.get("/product/")).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useGetProductById = (payload?: { id?: MaybeRef<string> } & TQuery<IProduct>) => {
   const options = computed(() => ({
      queryKey: ["product", "productById", payload?.id, payload?.language],
      queryFn: async ({ signal }: { signal: AbortSignal }) => {
         return (await appAxios.get(`/product/${toValue(payload?.id)}`, { signal, params: { lang_id: toValue(payload?.language) || 1 } })).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useUpdateProduct = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["product", "updateProduct"],
      mutationFn: async (data: IProductStore): Promise<TResponse<IProduct>> => {
         return (await appAxios.put("/product/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["product"] });
      }
   });
};

export const useCreateProduct = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["product", "createProduct"],
      mutationFn: async (data: IProductStore): Promise<TResponse<IProduct>> => {
         return (await appAxios.post("/product/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["product"] });
      }
   });
};

export const useDeleteProduct = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["product", "deleteProduct"],
      mutationFn: async (id: number): Promise<TResponse<boolean>> => {
         return (await appAxios.delete(`/product/${id}`)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["product"] });
      }
   });
};
