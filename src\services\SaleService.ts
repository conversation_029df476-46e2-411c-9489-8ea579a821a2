export interface ISale extends IDefaultFields {
   id: number;
   customer_id: number;
   product: string;
   price: string;
   installment: number;
   installment_type: string;
   notes?: string;
   customer?: { id: number; name: string; surname: string };
}

export interface ISaleStore {
   id?: number;
   customer_id: number;
   product: string;
   price: number;
   installment: number;
   installment_type: string;
   notes?: string;
}

export const useGetSaleAll = (payload?: TQuery<ISale[]>) => {
   const options = computed(() => ({
      queryKey: ["sale", "saleAll"],
      queryFn: async () => {
         const response = (await appAxios.get("/sale/")).data;
         // Her satış için müşteri bilgilerini al
         if (response.data && Array.isArray(response.data)) {
            const salesWithCustomers = await Promise.all(
               response.data.map(async (sale: ISale) => {
                  try {
                     const customerResponse = await appAxios.get(`/sale/${sale.id}/customer`);
                     return {
                        ...sale,
                        customer: customerResponse.data.data
                     };
                  } catch (error) {
                     return sale;
                  }
               })
            );
            return {
               ...response,
               data: salesWithCustomers
            };
         }
         return response;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useGetSaleById = (payload?: { id?: MaybeRef<string> } & TQuery<ISale>) => {
   const options = computed(() => ({
      queryKey: ["sale", "saleById", payload?.id, payload?.language],
      queryFn: async ({ signal }: { signal: AbortSignal }) => {
         return (await appAxios.get(`/sale/${toValue(payload?.id)}`, { signal, params: { lang_id: toValue(payload?.language) || 1 } })).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useUpdateSale = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["sale", "updateSale"],
      mutationFn: async (data: ISaleStore): Promise<TResponse<ISale>> => {
         return (await appAxios.put("/sale/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["sale"] });
      }
   });
};

export const useCreateSale = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["sale", "createSale"],
      mutationFn: async (data: ISaleStore): Promise<TResponse<ISale>> => {
         return (await appAxios.post("/sale/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["sale"] });
      }
   });
};

export const useDeleteSale = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["sale", "deleteSale"],
      mutationFn: async (id: number): Promise<TResponse<ISale>> => {
         return (await appAxios.delete(`/sale/${id}`)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["sale"] });
      }
   });
};

export const useGetSaleCustomer = (payload?: { id?: MaybeRef<string | number> } & TQuery<any>) => {
   const options = computed(() => ({
      queryKey: ["sale", "saleCustomer", payload?.id],
      queryFn: async ({ signal }: { signal: AbortSignal }) => {
         return (await appAxios.get(`/sale/${toValue(payload?.id)}/customer`, { signal })).data;
      },
      enabled: payload?.enabled && !!payload?.id
   }));

   return queryWrapper(options, payload);
};