export interface IUser extends IDefaultFields {
   id: number;
   username: string;
   name: string;
   surname: string;
   email?: string;
   password: string;
   phone?: string;
   role: string;
}

export interface IUserStore {
   id?: number;
   username: string;
   name: string;
   surname: string;
   email?: string;
   password: string;
   phone?: string;
   role: string;
}

export const useGetUserAll = (payload?: TQuery<IUser[]>) => {
   const options = computed(() => ({
      queryKey: ["user", "userAll"],
      queryFn: async () => {
         return (await appAxios.get("/user/")).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useGetUserById = (payload?: { id?: MaybeRef<string> } & TQuery<IUser>) => {
   const options = computed(() => ({
      queryKey: ["user", "userById", payload?.id, payload?.language],
      queryFn: async ({ signal }: { signal: AbortSignal }) => {
         return (await appAxios.get(`/user/${toValue(payload?.id)}`, { signal, params: { lang_id: toValue(payload?.language) || 1 } })).data;
      },
      enabled: payload?.enabled
   }));

   return queryWrapper(options, payload);
};

export const useUpdateUser = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["user", "updateUser"],
      mutationFn: async (data: IUserStore): Promise<TResponse<IUser>> => {
         return (await appAxios.put("/user/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["user"] });
      }
   });
};

export const useCreateUser = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["user", "createUser"],
      mutationFn: async (data: IUserStore): Promise<TResponse<IUser>> => {
         return (await appAxios.post("/user/", data)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["user"] });
      }
   });
};

export const useDeleteUser = () => {
   const queryClient = useQueryClient();
   return useMutation({
      mutationKey: ["user", "deleteUser"],
      mutationFn: async (id: number): Promise<TResponse<boolean>> => {
         return (await appAxios.delete(`/user/${id}`)).data;
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ["user"] });
      }
   });
};