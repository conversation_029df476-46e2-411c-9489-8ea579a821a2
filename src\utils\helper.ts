export const VITE_MEDIA = import.meta.env.VITE_MEDIA;

/**
 * Async component loader
 * @example
 * getComponent(() => import("../components/Loader/LayoutLoader.vue"))
 */
export const getComponent = (promise: () => Promise<Component>): (() => Promise<Component>) => {
   return async () => {
      const appStore = useAppStore();

      appStore.setComponentLoading(true);
      return await promise()
         .then((mod) => mod)
         .finally(() => {
            appStore.setComponentLoading(false);
         });
   };
};

/**
 * Async component loader with retry
 * @example
 * getComponentAsync("component.vue", ErrorLayout)
 */
export const getComponentAsync = (component: string, error: Component): Component => {
   const appStore = useAppStore();

   return defineAsyncComponent({
      loader: async () => {
         // await timerSleep(5000);
         return await import(`@/components/layouts/${component}/Layout.vue`).then((mod) => {
            appStore.setLayoutLoading(false);
            return mod.default;
         });
      },
      onError(error, retry, fail, attempts) {
         const retryDelay = timerAttempt(attempts - 1);

         if (error && attempts <= appConfig.retry.attempt) {
            setTimeout(() => {
               retry();
            }, retryDelay);
         } else {
            appStore.setLayoutLoading(false);
            fail();
         }
      },
      delay: 0,
      errorComponent: error
   });
};

/**
 * Tanstack Query data
 * @example
 * queryData("key")
 */
export const queryData = (key: any): { [key: string]: any } => {
   const queryClient = useQueryClient();
   return queryClient.getQueryData([key]) || [];
};

/**
 * Tanstack Query prefetch
 * @example
 * queryPrefetch("key", () => fetch("url"))
 */
export const queryPrefetch = async (key: string, callback: Function) => {
   const queryClient = useQueryClient();
   await queryClient.prefetchQuery({
      queryKey: [key],
      queryFn: callback.call(this)
   });
};

/**
 * Tanstack Query wrapper
 * @example
 * queryWrapper(options, payload)
 */
export const queryWrapper = <T>(options: UseQueryOptions<T>, payload?: TQuery<T>) => {
   const query = <UseQueryReturnType<T, Error>>useQuery<T>(toValue(options));
   const isFirst = ref(true);

   watch(
      () => query.data.value,
      () => {
         if (query.isSuccess.value && query.data.value) {
            isFirst.value = false;
            payload?.onSuccess?.(query.data.value);
         }
      },
      { immediate: true }
   );

   watch(
      () => query.isError.value,
      () => {
         if (query.isError.value && query.error.value) {
            payload?.onError?.(query.error.value);
         }
      },
      { immediate: true }
   );

   const isLoading = computed(() => query.isLoading.value && isFirst.value);

   return { ...query, isLoading };
};

/**
 * Kullanıcının tercih ettiği dili değiştirir ve `localStorage[appConfig.key.locale]` anahtarına kaydeder.
 * @example
 * setUserLocale("tr-TR") => Dil Türkçe olarak değişir.
 */
export const setUserLocale = (locale: string) => {
   document.documentElement.setAttribute("lang", locale);
   localStorage.setItem(appConfig.key.locale, locale);
   i18n.global.locale.value = locale;
};

/**
 * Kullanıcının tercih ettiği dili `localStorage[appConfig.key.locale]` anahtarından alır.
 * @example
 * getUserLocale() => "tr-TR"
 */
export const getUserLocale = (fallback: boolean = true) => {
   const locale = localStorage.getItem(appConfig.key.locale);

   if (fallback) {
      if (locale && Object.keys(appConfig.language.locales).includes(locale)) {
         return locale;
      }

      return appConfig.language.default;
   }

   return locale || (null as any);
};

const json: string[] = [];
/**
 * Verilen dile ait dil dosyalarını yükler. Yüklenen dosyalar ön belleğe alınır ve tekrar yüklenmez.
 */
export const loadLocales = async (locale: string): Promise<void> => {
   if (!json.includes(locale)) {
      const [common, vuetify, ...modules] = await Promise.all([
         import(`@/locales/${locale}.json`).then((res) => res.default).catch(() => {}),
         import("vuetify/locale").then((res: any) => res[locale.split("-")[0].toLowerCase()]).catch(() => {}),
         ...appConfig.module.map((item) => import(`@/modules/${item}/locales/${locale}.json`).then((res) => res.default).catch(() => {}))
      ]);

      json.push(locale);

      i18n.global.setLocaleMessage(locale, {
         ...common,
         ...Object.assign({}, ...modules),
         $vuetify: { ...vuetify }
      });
   }

   return nextTick();
};

/**
 * Dil dosyalarını sunucudan yükler.
 */
export const fetchLocales = async () => {};

/**
 * Modüllerdeki menü dosyalarını yükler.
 */
export const loadMenu = async (): Promise<Record<string, TList[]>> => {
   const menu = await Promise.all(
      appConfig.module.map(async (module) => {
         try {
            const mod = await import(`@/modules/${module}/utils/menu.ts`);
            const items = Object.values(mod)
               .filter((exported) => Array.isArray(exported))
               .flat();

            return { [module.toLowerCase()]: items };
         } catch (err) {
            return { [module.toLowerCase()]: [] };
         }
      })
   );

   return Object.assign({}, ...menu);
};

/**
 * Modüllerdeki route dosyalarını yükler.
 */
export const loadRoutes = async (): Promise<RouteRecordRaw[]> => {
   const routes = await Promise.all(
      appConfig.module.map(async (module) => {
         try {
            const mod = await import(`@/modules/${module}/utils/routes.ts`);
            return Object.values(mod)
               .filter((exported) => Array.isArray(exported))
               .flat();
         } catch (err) {
            return [];
         }
      })
   );

   return routes.flat();
};

/**
 * Kullanıcının tercih ettiği temayı değiştirir ve `localStorage[appConfig.key.theme]` anahtarına kaydeder.
 * @example
 * setUserTheme(theme, "dark") => Tema dark olarak değişir.
 * setUserTheme(theme, true)   => Mevcut tema dark ise light, light ise dark olarak değişir.
 */
export const setUserTheme = (theme: ThemeInstance, mode: "light" | "dark" | true): void => {
   if (mode === true) {
      mode = theme.current.value.dark ? "light" : "dark";
   }

   theme.change(mode);
   localStorage.setItem(appConfig.key.theme, mode);
};

/**
 * Kullanıcının tercih ettiği temayı `localStorage[appConfig.key.theme]` anahtarından alır.
 */
export const getUserTheme = (fallback: string): string => {
   const theme = localStorage.getItem(appConfig.key.theme);
   return theme && ["light", "dark"].includes(theme) ? theme : fallback;
};

/**
 * Verilen metni slug formatına çevirir.
 * @example
 * formatSlug("Merhaba Dünya") => "merhaba-dunya"
 */
export const formatSlug = (input: any): string => {
   if (!input) {
      return "";
   }

   const trMap: Record<string, string> = { ş: "s", Ş: "s", ı: "i", İ: "i", ç: "c", Ç: "c", ü: "u", Ü: "u", ö: "o", Ö: "o", ğ: "g", Ğ: "g" };

   input = String(input)
      .replace(/[şŞıİçÇüÜöÖğĞ]/g, (ch) => trMap[ch])
      .toLowerCase()
      .replace(/&/g, " and ")
      .replace(/[().\/+*]/g, "")
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/-+/g, "-")
      .replace(/^-|-$/g, "");

   return input;
};

/**
 * Verilen formatı belirtilen argümanlarla doldurur.
 * @example
 * formatString("Merhaba {0}, selamlar", ["dünya"]) => "Merhaba dünya, selamlar"
 * formatString("Merhaba {0,1} {1}", [["dünya", "venus"], ["selamlar"]]) => "Merhaba venus selamlar"
 * formatString("Merhaba {0,0} {1}", [["dünya", "venus"], ["selamlar"]]) => "Merhaba dünya selamlar"
 */
export const formatString = (format: string, args: any[]): string => {
   return format.replace(/{(\d+)(?:,(\d+))?}/g, (match, index, sub) => {
      let value = args[index];

      if (typeof sub !== "undefined" && Array.isArray(value)) {
         value = value[sub];
      }

      return typeof value !== "undefined" ? value : match;
   });
};

/**
 * Veritabanından gelen DECIMAL (string) değerini Türkiye para yazım formatına çevirir.
 * @example
 * formatMoney("2155.00") => "2.155,00"
 * formatMoney("")        => "0,00"
 */
export const formatMoney = (str: string = "0.00"): string => {
   return parseFloat(str).toLocaleString("tr-TR", { minimumFractionDigits: 2 });
};

/**
 * Önyüzdeki para yazım formatını veritabanı formatına çevirir.
 * @example
 * formatDecimal("2.155,00") => 2155.00
 * formatDecimal("")         => 0.00
 */
export const formatDecimal = (str: string = "0,00"): number => {
   const normalized = str.replace(/\./g, "").replace(",", ".");
   return parseFloat(normalized);
};

/**
 * Önyüzdeki eksik basamakları doldurur.
 * @example
 * formatFraction("")         => "0,00"
 * formatFraction("2.155")    => "2.155,00"
 * formatFraction("2.155,1")  => "2.155,10"
 * formatFraction("2.155,11") => "2.155,11"
 */
export const formatFraction = (str: string): string => {
   if (!str) return "0,00";

   const [number, pad = ""] = str.split(",");
   return `${number},${pad.padEnd(2, "0")}`;
};

/**
 * Basamakların solunu sıfırlarla doldurur. Varsayılan basamak sayısı `2`'dir.
 * @example
 * formatPadding(-1)   => "-01"
 * formatPadding(9)    => "09"
 * formatPadding(9, 3) => "009"
 */
export const formatPadding = (value: number, pad: number = 2): string => {
   const result = String(Math.abs(value)).padStart(pad, "0");
   return value < 0 ? "-" + result : result;
};

/**
 * Verilen değeri kısaltarak basamak sayısını azaltır.
 * @example
 * formatCounter(1000)    => 1k
 * formatCounter(10000)   => 10k
 * formatCounter(100000)  => 100k
 * formatCounter(1000000) => 1m
 */
export const formatCounter = (num: number): string => {
   if (num < 1000) {
      return num.toString();
   }

   const units = ["k", "m", "b", "t"];
   const exp = Math.floor(Math.log(num) / Math.log(1000));

   return num / Math.pow(1000, exp) + units[exp - 1];
};

/**
 * Verilen değeri dosya boyutu birimlerine çevirir.
 * @example
 * formatSize(1024)               => 1KB
 * formatSize(1024 * 1024)        => 1MB
 * formatSize(1024 * 1024 * 1024) => 1GB
 */
export const formatSize = (bytes: number): string => {
   if (bytes < 1024) {
      return bytes.toString() + "B";
   }

   const units = ["B", "KB", "MB", "GB", "TB", "PB"];
   const exp = Math.floor(Math.log(bytes) / Math.log(1024));

   return parseFloat((bytes / Math.pow(1024, exp)).toFixed(3)) + units[exp];
};

/**
 * Verilen değeri belirtilen format ve dil seçeneklerine göre çevirir.
 * @example
 * formatDate(new Date()) => "25.04.1986" (Günün tarihi)
 * formatDate(new Date(),{ dateStyle: "full" }) => "Friday, Apr 25, 2025"
 * @link
 * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat
 */
export const formatDate = (date: Date | string, options: Intl.DateTimeFormatOptions = {}, locale: string = getUserLocale()): string | null => {
   const { dateStyle, timeStyle } = options;
   const defaultOptions = {
      ...(appConfig.format.date as Intl.DateTimeFormatOptions),
      ...options
   };

   if (!date) {
      return null;
   }

   if (date instanceof Date === false) {
      date = new Date(date);
   }

   if (isNaN(date.getTime())) {
      return null;
   }

   return new Intl.DateTimeFormat(locale, dateStyle || timeStyle ? options : defaultOptions).format(date);
};

/**
 * Verilen tarihin saat, dakika, saniye ve milisaniye değeri aynı anda `0` ise milisaniyeyi `999` yapar.
 * Tümü `0` olduğunda veritabanı tarih karşılaştırmasında sorun çıkabiliyor.
 * @example
 * formatMS(new Date()) => 1986-04-25 00:00:00:999 (00:00:00:000 => 00:00:00:999)
 */
export const formatMS = (value: Date | string): Date => {
   const date = new Date(value);
   if (date.getHours() === 0 && date.getMinutes() === 0 && date.getSeconds() === 0 && date.getMilliseconds() === 0) {
      date.setMilliseconds(999);
   }
   return date;
};

/**
 * `oklch` formatındaki rengi `hex` formatına çevirir.
 * @example
 * formatOklch("oklch(0.359 0.144 278.697)") => "#f0f0f0"
 */
export const formatOklch = (color: string): string => {
   // Regex to extract the values from the oklch() string
   const match = color.match(/oklch\(([\d.]+%)\s([\d.]+)\s([\d.]+)/);
   if (!match) throw new Error("Invalid OKLCH color format");

   // Parse the values from the regex match
   const L = parseFloat(match[1]) / 100;
   const C = parseFloat(match[2]);
   const H = (parseFloat(match[3]) * Math.PI) / 180;

   // OKLCH to OKLab
   const a = C * Math.cos(H);
   const b = C * Math.sin(H);

   // OKLab to linear sRGB
   const l_ = L + 0.3963377774 * a + 0.2158037573 * b;
   const m_ = L - 0.1055613458 * a - 0.0638541728 * b;
   const s_ = L - 0.0894841775 * a - 1.291485548 * b;

   const l = Math.pow(l_, 3);
   const m = Math.pow(m_, 3);
   const s = Math.pow(s_, 3);

   // Linear sRGB to sRGB
   const r = Math.max(0, Math.min(1, +4.0767416621 * l - 3.3077115913 * m + 0.2309699292 * s));
   const g = Math.max(0, Math.min(1, -1.2684380046 * l + 2.6097574011 * m - 0.3413193965 * s));
   const bColor = Math.max(0, Math.min(1, -0.0041960863 * l - 0.7034186147 * m + 1.707614701 * s));

   // Convert to 8-bit per channel and format as hex
   const toHex = (value: number) => {
      const hex = Math.round(value * 255)
         .toString(16)
         .padStart(2, "0");
      return hex;
   };

   return `#${toHex(r)}${toHex(g)}${toHex(bColor)}`;
};

/**
 * Zaman damgalı rastgele bir `GUID` üretir. Zaten üretilmiş olan `GUID`'lerin tekrar üretilmemesi için `haystack`
 * parametresi ile mevcut `GUID`'lerin listesini de gönderebilirsiniz.
 * @example
 * generateRandomGuid() => "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx"
 */
export const generateRandomGuid = (haystack?: string[]): string => {
   const timestamp = Date.now().toString(16).slice(-8);
   const uuid = "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (char: string) => {
      let rand = (Math.random() * 16) % 16 | 0;
      let result = char === "x" ? rand : (rand & 0x3) | 0x8;
      return result.toString(16);
   });

   if (haystack?.includes(uuid)) {
      return generateRandomGuid(haystack);
   }

   return uuid.slice(0, -8) + timestamp;
};

/**
 * Rastgele bir dize üretir.
 * @example
 * generateRandomString() => "aBcDeFgHiJ"
 * generateRandomString(8, { upperCase: false, lowerCase: true, numbers: false }) => "abcdefgh"
 */
export const generateRandomString = (length: number = 8, options: { upperCase?: boolean; lowerCase?: boolean; numbers?: boolean } = {}): string => {
   const defaultOptions = {
      upperCase: true,
      lowerCase: true,
      numbers: true,
      ...options
   };
   const { upperCase, lowerCase, numbers } = defaultOptions;
   const characters = {
      upperCase: "ABCDEFGHIJKLMNOPQRSTUVWXYZ",
      lowerCase: "abcdefghijklmnopqrstuvwxyz",
      numbers: "0123456789"
   };

   let result = "";
   if (numbers) {
      result = result.concat(characters.numbers);
   }

   if (upperCase) {
      result = result.concat(characters.upperCase);
   }

   if (lowerCase) {
      result = result.concat(characters.lowerCase);
   }

   return Array.from({ length }, () => result.charAt(Math.floor(Math.random() * result.length)))
      .sort(() => 0.5 - Math.random())
      .join("");
};

/**
 * İşlemler belirtilen süre içinde tekrar çağrılırsa önceki çağrıyı iptal eder ve yeni bir çağrı başlatır.
 * @example
 * timerDebounce(() => console.log("debounce"), 350)
 * @link
 * https://css-tricks.com/debouncing-throttling-explained-examples/
 */
export const timerDebounce = (callback: (...args: any[]) => void, delay: number = 350, instant: any = true): Function => {
   let timeout: any = false;

   return (...args: any) => {
      if (timeout) {
         clearTimeout(timeout);
      } else if (instant) {
         callback(...args);
      }

      timeout = setTimeout(() => {
         callback(...args);
         timeout = false;
      }, delay);
   };
};

/**
 * İşlemler belirtilen süre içinde tekrar çağrılırsa önceki çağrıyı iptal etmez ve belirtilen süre içinde sadece bir kez çalışır.
 * @example
 * timerThrottle(() => console.log("throttle"), 350)
 * @link
 * https://css-tricks.com/debouncing-throttling-explained-examples/
 */
export const timerThrottle = (callback: any, delay: number, instant: any = true): object => {
   let timeout: any = false;
   let moment: any = false;
   return (...args: any) => {
      if (!moment) {
         if (timeout === false && instant) {
            callback.apply(this, args);
         }
         moment = Date.now();
      } else {
         clearTimeout(timeout);
         timeout = setTimeout(
            () => {
               if (Date.now() - moment >= delay) {
                  callback.apply(this, args);
                  moment = Date.now();
               }
            },
            delay - (Date.now() - moment)
         );
      }
   };
};

/**
 * Tekrar deneme süresini hesaplar.
 * @example
 * timerAttempt(0) => 1000
 * timerAttempt(1) => 2000
 * timerAttempt(2) => 4000
 * timerAttempt(3) => 8000
 * timerAttempt(4) => 10000 (max)
 */
export const timerAttempt = (attempt: number, max: number = 30000) => {
   if (appConfig.retry.gradual) {
      return Math.min(appConfig.retry.delay * 2 ** attempt, max);
   } else {
      return appConfig.retry.delay;
   }
};

/**
 * Belirtilen süre kadar bekler.
 * @example
 * timerSleep(1000) => 1000ms sonra resolve eder.
 */
export const timerSleep = (delay: number = 250): Promise<void> => {
   return new Promise((resolve) => (delay ? setTimeout(resolve, delay) : resolve()));
};

/**
 * Form verilerini `FormData` nesnesine çevirir.
 * @example
 * createFormData({ name: "John", age: 30, email: "<EMAIL>" })
 * createFormData({ name: "John", age: 30, email: "<EMAIL>" }, { files: [file1, file2] })
 */
export const createFormData = <T extends Record<string, any>>(form: T, additional: Record<string, any> = {}): FormData => {
   const formData = new FormData();

   for (const key in form) {
      const value: any = form[key];

      if (value === undefined || value === null) continue;

      if (Array.isArray(value)) {
         if (value[0] instanceof File) {
            value.forEach((file: File) => formData.append(key + "[]", file));
         } else if (typeof value[0] === "object") {
            formData.append(key, JSON.stringify(value));
         } else {
            value.forEach((item: any) => formData.append(key + "[]", item));
         }
      } else if (value instanceof File || value instanceof Blob) {
         formData.append(key, value);
      } else if (typeof value === "object") {
         formData.append(key, JSON.stringify(value));
      } else {
         formData.append(key, value);
      }
   }

   for (const key in additional) {
      const value = additional[key];
      if (value instanceof File || value instanceof Blob) {
         formData.append(key, value);
      } else {
         formData.append(key, typeof value === "object" ? JSON.stringify(value) : value);
      }
   }

   return formData;
};

/**
 * Dosya yükleme işlemleri için `FormData` nesnesi oluşturur.
 * @example
 * imageFormData([file1, file2], { path: "product" })
 */
export const imageFormData = <T extends Record<string, any>>(files: T[] = [], additional: Record<string, any> = {}) => {
   const formData = new FormData();

   // files.forEach((file) => formData.append("files[]", file));
   for (const key in files) {
      const value = files[key];
      if (value instanceof File || value instanceof Blob) {
         formData.append(key, value);
      }
   }

   for (const key in additional) {
      const value = additional[key];
      if (value instanceof File || value instanceof Blob) {
         formData.append(key, value);
      } else {
         formData.append(key, typeof value === "object" ? JSON.stringify(value) : value);
      }
   }

   return formData;
};

/**
 * Verilen verilerden varsayılan değerleri veya belirtilen değeri alır.
 * @example
 * setInitialData([{ id: 1, name: "John", default: "John" }, { id: 2, name: "Doe", default: "Doe" }], "name", "Doe")
 */
export const setInitialData = (items: any, key: string, value: any) => {
   const initialData: { [key: string]: any } = {};
   items.forEach((item: any) => (initialData[item[key]] = item.default || value));
   return initialData;
};

/**
 * Google Translate API'sini kullanarak verilen metni çevirir.
 * @example
 * useTranslate("Merhaba dünya", "tr", "en") => "Hello world"
 */
export function useTranslate() {
   const isLoading = ref(false);
   const isError = ref(false);
   const data = ref<string | null>("");

   const translate = async (text: string, from = "tr", to = "en"): Promise<any> => {
      isLoading.value = true;
      isError.value = false;
      data.value = "";

      try {
         const response = await fetch(`https://translate.googleapis.com/translate_a/single?client=gtx&sl=${from}&tl=${to}&dt=t&q=${encodeURIComponent(text)}`);
         const json = await response.json();
         // data.value = json[0][0][0];
         for (const item of json[0]) {
            data.value += item[0];
         }
         return data.value;
      } catch (error) {
         isError.value = true;
         data.value = text;
         return text;
      } finally {
         isLoading.value = false;
      }
   };

   return { translate, isLoading, isError, data };
}

// inputRegex
/**
 * Verilen sorguyu regex formatına çevirir. Sayısal değerler için karşılaştırma operatörleri kullanılabilir.
 * @example
 * inputRegex("hello")      => "/hello/i"
 * inputRegex(">5", true)   => value > 5
 * inputRegex("<10", true)  => value < 10
 * inputRegex("=15", true)  => value === 15
 * inputRegex("!20", true)  => value !== 20
 * inputRegex(">=25", true) => value >= 25
 * inputRegex("<=30", true) => value <= 30
 */
export const inputRegex = (query: any = null, numeric: boolean = false) => {
   if (query === null) {
      return "";
   }

   const match = query.match(/^([><=!])\s*([\d.]+)$/);
   if (match && numeric) {
      const operator = match[1];
      const threshold = parseFloat(match[2]);
      return (value: any) => {
         return (
            value !== null &&
            typeof value === "number" &&
            ((operator === ">" && value > threshold) ||
               (operator === "<" && value < threshold) ||
               (operator === "=" && value === threshold) ||
               (operator === "!" && value !== threshold))
         );
      };
   }

   query = lowerCase(query)
      .replace(/[.+?^${}()|[\]\\]/g, "\\$&")
      .replace(/\*/g, ".*")
      .replace(/\//g, "^")
      .replace(/[üÜuU]/g, "[üÜuU]")
      .replace(/[iİıI]/g, "[iİıI]")
      .replace(/[ğĞgG]/g, "[ğĞgG]")
      .replace(/[şŞsS]/g, "[şŞsS]")
      .replace(/[çÇcC]/g, "[çÇcC]")
      .replace(/[öÖoO]/g, "[öÖoO]");

   return new RegExp(`${query}`, "i");
};

/**
 * Verilen sorguyu regex formatına çevirir ve verilen verilerde arama yapar.
 * @example
 * inputFilter("hello", "hello world") => true
 * inputFilter(">5", 10, null, true) => true
 */
export const inputFilter = (items: any, query: any, _item?: any, numeric: boolean = false) => {
   const regex = inputRegex(query, numeric);

   if (typeof regex === "function") {
      return items !== null && query !== null && regex(items);
   }

   return items !== null && query !== null && items.search(regex) !== -1;
};

/**
 * Provider oluşturma
 * @link https://vuejsdevelopers.com/2020/10/05/composition-api-vuex/
 */
export const setProvider = (provider: { [key: string]: any }) => {
   const state = ref(provider.state);

   const methods = Object.keys(provider.methods).reduce(
      (acc, methodName) => {
         acc[methodName] = function (...args: any) {
            const callback = args.findIndex((arg: any) => typeof arg === "function");
            if (callback > -1) {
               return provider.methods[methodName](state, ...args.slice(0, callback), args[callback].bind(this));
            }
            return provider.methods[methodName](state, ...args);
         };
         return acc;
      },
      {} as { [key: string]: Function }
   );

   return {
      state: typeof state,
      ...methods
   };
};

/**
 * Provider okuma
 */
export const getProvider = (provider: string) => {
   return inject(provider);
};

/**
 * Dosyanın uzantısını döndürür.
 * @example
 * getFileExtension(blob) => "jpg"
 */
export const getFileExtension = (blob: any): string => {
   return blob.type || blob.type.split["/"][1];
};

/**
 * Büyük harfe çevirir.
 * @example
 * upperCase("hello world") => "HELLO WORLD"
 */
export const upperCase = (value: any, locale: string = getUserLocale()): string => {
   return value.toLocaleUpperCase(locale);
};

/**
 * Küçük harfe çevirir.
 * @example
 * lowerCase("HELLO WORLD") => "hello world"
 */
export const lowerCase = (value: any, locale: string = getUserLocale()): string => {
   return value.toLocaleLowerCase(locale);
};

/**
 * Her kelimenin ilk harfini büyük harfe çevirir.
 * @example
 * ucWords("hello world") => "Hello World"
 */
export const ucWords = (value: any): string => {
   return lowerCase(value).replace(/(^\p{L})|(\s+\p{L})/gu, (char) => {
      return upperCase(char);
   });
};

/**
 * İlk harfi büyük harfe çevirir.
 * @example
 * ucFirst("hello world") => "Hello world"
 */
export const ucFirst = (value: any): string => {
   return upperCase(value.charAt(0)) + lowerCase(value.slice(1));
};

// escape url
/**
 * Bir dosya yolu veya URL ifadesini güvenli ve geçerli bir URL biçimine dönüştürür.
 * URL'deki ters eğik çizgileri düz eğik çizgiye çevirir ve URL'i kodlar.
 * @example
 * escapeUrl("hello\\world") => "hello/world"
 * escapeUrl("hello/world")  => "hello/world"
 * escapeUrl("hello world")  => "hello%20world"
 */
export const escapeUrl = (url: any): string => {
   url = url.replace(/\\/g, "/");
   return encodeURI(url);
};

/**
 * Sürükle bırak işlemi sırasında elementin görünümünü gizler.
 * @example
 * dragHide(event)
 */
export const dragHide = (e: any): void => {
   let crt = e.target.cloneNode(true);
   crt.style.display = "none";
   e.dataTransfer.setDragImage(crt, 0, 0);
};

/**
 * Elemanın konumunu döndürür.
 * @example
 * position.offset(node) => { top: 0, left: 0, width: 0, height: 0 }
 * position.client(node) => { top: 0, left: 0, width: 0, height: 0 }
 * position.scroll(node) => { top: 0, left: 0, width: 0, height: 0 }
 * position.rect(node)   => { x: 0, y: 0, top: 0, right: 0, bottom: 0, left: 0, width: 0, height: 0 }
 */
export const position = {
   offset: function (node: HTMLElement, view = true) {
      let rect: any = node.getBoundingClientRect();
      let owner: any = node.ownerDocument.defaultView;

      return {
         top: view === false ? node.offsetTop : Math.round(rect.top + owner.pageYOffset),
         left: view === false ? node.offsetLeft : Math.round(rect.left + owner.pageXOffset),
         width: node.offsetWidth,
         height: node.offsetHeight
      };
   },

   client: function (node: HTMLElement) {
      return {
         top: node.clientTop,
         left: node.clientLeft,
         width: node.clientWidth,
         height: node.clientHeight
      };
   },

   scroll: function (node: HTMLElement) {
      let win: any = window;
      let doc: any = document.documentElement;

      return {
         top: node === win ? (win.scrollY || doc.scrollTop) - (doc.clientTop || 0) || win.scrollY : node.scrollTop,
         left: node === win ? (win.scrollX || doc.scrollLeft) - (doc.clientLeft || 0) || win.scrollX : node.scrollLeft,
         width: node.scrollWidth,
         height: node.scrollHeight
      };
   },

   rect: function (node: HTMLElement) {
      let rect = node.getBoundingClientRect();
      let ie = navigator.userAgent.indexOf("MSIE") !== -1;
      let top = ie && node.tagName === "HTML" ? -node.scrollTop : rect.top;

      return {
         x: Math.round(rect.x),
         y: Math.round(rect.y),
         top: Math.round(top),
         right: Math.round(rect.right),
         bottom: Math.round(rect.bottom),
         left: Math.round(rect.left),
         width: Math.round(rect.width),
         height: Math.round(rect.height)
      };
   }
};
