export const registerMenu = async (): Promise<void> => {
   const appStore = useAppStore();
   appStore.setMenuLoading(true);
   await loadMenu()
      .then((menu) => {
         appStore.setMenu(menu);
      })
      .finally(() => {
         appStore.setMenuLoading(false);
      });
};

export const appMenu: TList[] = [
   {
      itemType: "subheader",
      itemTitle: "Menu"
   },
   {
      itemTitle: i18n.global.t("app.product", 2),
      itemProps: {
         prependIcon: "$product",
         to: "/product"
      }
   },
   {
      itemTitle: i18n.global.t("app.category", 2),
      itemProps: {
         prependIcon: "$category",
         to: "/category"
      }
   },
   {
      itemTitle: i18n.global.t("app.customer", 2),
      itemProps: {
         prependIcon: "$customer",
         to: "/customer"
      }
   },
   {
      itemTitle: i18n.global.t("app.sale", 2),
      itemProps: {
         prependIcon: "$sale",
         to: "/sale"
      }
   },
   {
      itemTitle: i18n.global.t("app.manufacturer", 2),
      itemProps: {
         prependIcon: "$manufacturer",
         to: "/manufacturer"
      }
   },
   {
      itemTitle: i18n.global.t("app.standard", 2),
      itemProps: {
         prependIcon: "$standard",
         to: "/standard"
      }
   },
   {
      itemTitle: i18n.global.t("app.attribute", 2),
      itemProps: {
         prependIcon: "$attribute",
         to: "/attribute"
      }
   }
];
