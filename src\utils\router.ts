import DefaultLayout from "@/components/Layout/Default/Layout.vue";
import ErrorLayout from "@/components/Layout/Error/Layout.vue";

declare module "vue-router" {
   interface RouteMeta {
      layout?: any;
      title?: string;
      breadcrumb?: string;
      auth?: boolean;
      module?: string;
   }
}

export type TRoute = RouteLocationNormalizedLoaded & {
   params: {
      id: string;
   };
};

export const registerRoutes = async (): Promise<Router> => {
   (await loadRoutes()).forEach((route) => {
      router.addRoute(route);
   });
   return router;
};

const routes: RouteRecordRaw[] = [
   {
      path: "/:pathMatch(.*)",
      name: "NotFound",
      meta: {
         title: "404",
         breadcrumb: "404",
         layout: ErrorLayout
      },
      component: getComponent(() => import("@/components/Error/ErrorComponent.vue"))
   },
   {
      path: "/",
      redirect: appConfig.router.redirect,
      meta: {
         layout: DefaultLayout
      },
      children: [
         {
            path: "product",
            meta: {
               title: i18n.global.t("app.product"),
               breadcrumb: i18n.global.t("app.productList")
            },
            children: [
               {
                  path: "",
                  name: "productList",
                  component: getComponent(() => import("../pages/ProductList.vue"))
               },
               {
                  path: ":id(create|[0-9]+)",
                  name: "productDetail",
                  component: getComponent(() => import("../pages/ProductDetail.vue")),
                  meta: {
                     breadcrumb: "{ProductDetail}"
                  }
               }
            ]
         },
         {
            path: "category",
            meta: {
               title: i18n.global.t("app.category"),
               breadcrumb: i18n.global.t("app.categoryList")
            },
            children: [
               {
                  path: "",
                  name: "categoryList",
                  component: getComponent(() => import("../pages/CategoryList.vue"))
               },
               {
                  path: ":id(create|[0-9]+)",
                  name: "categoryDetail",
                  component: getComponent(() => import("../pages/CategoryDetail.vue")),
                  meta: {
                     breadcrumb: "{CategoryDetail}"
                  }
               }
            ]
         },
         {
            path: "customer",
            meta: {
               title: i18n.global.t("app.customer"),
               breadcrumb: i18n.global.t("app.customerList")
            },
            children: [
               {
                  path: "",
                  name: "customerList",
                  component: getComponent(() => import("../pages/CustomerList.vue"))
               },
               {
                  path: ":id(create|[0-9]+)",
                  name: "customerDetail",
                  component: getComponent(() => import("../pages/CustomerDetail.vue")),
                  meta: {
                     breadcrumb: "{CustomerDetail}"
                  }
               },
               {
                  path: ":id([0-9]+)/history",
                  name: "customerHistory",
                  component: getComponent(() => import("../pages/CustomerHistory.vue")),
                  meta: {
                     breadcrumb: "{CustomerHistory}"
                  }
               }
            ]
         },
         {
            path: "sale",
            meta: {
               title: i18n.global.t("app.sale"),
               breadcrumb: i18n.global.t("app.saleList")
            },
            children: [
               {
                  path: "",
                  name: "saleList",
                  component: getComponent(() => import("../pages/SaleList.vue"))
               },
               {
                  path: ":id(create|[0-9]+)",
                  name: "saleDetail",
                  component: getComponent(() => import("../pages/SaleDetail.vue")),
                  meta: {
                     breadcrumb: "{SaleDetail}"
                  }
               }
            ]
         },
         {
            path: "installment",
            meta: {
               title: i18n.global.t("app.installment"),
               breadcrumb: i18n.global.t("app.installmentList")
            },
            children: [
               {
                  path: "",
                  name: "installmentList",
                  component: getComponent(() => import("../pages/InstallmentList.vue"))
               },
               {
                  path: ":id(create|[0-9]+)",
                  name: "installmentDetail",
                  component: getComponent(() => import("../pages/InstallmentDetail.vue")),
                  meta: {
                     breadcrumb: "{InstallmentDetail}"
                  }
               }
            ]
         },
         {
            path: "user",
            meta: {
               title: i18n.global.t("app.user"),
               breadcrumb: i18n.global.t("app.userList")
            },
            children: [
               {
                  path: "",
                  name: "userList",
                  component: getComponent(() => import("../pages/UserList.vue"))
               },
               {
                  path: ":id(create|[0-9]+)",
                  name: "userDetail",
                  component: getComponent(() => import("../pages/UserDetail.vue")),
                  meta: {
                     breadcrumb: "{UserDetail}"
                  }
               }
            ]
         }
      ]
   }
];

export const router = createRouter({
   history: createWebHistory(),
   routes,
   scrollBehavior(_to, _from, savedPosition) {
      if (!savedPosition) {
         return new Promise((resolve) => {
            setTimeout(() => {
               const scroller = document.querySelector("#main");
               if (scroller) scroller.scrollTo({ top: 0, behavior: "instant" });
               resolve(false);
            }, 50);
         });
      }
   }
});

router.beforeEach(async (to, from, next) => {
   const appStore = useAppStore();

   if (from.meta.layout !== to.meta.layout) {
      appStore.setLayoutLoading(true);
   }

   appStore.setModule(to.meta.module);
   next();
});

router.afterEach(async (to) => {
   const appStore = useAppStore();
   const authStore = useAuthStore();

   if (to.path !== appConfig.router.login) {
      authStore.setUrl(to.fullPath);
   }

   await timerSleep();
   appStore.setLayoutLoading(false);
});

// router.beforeEach(async (to, _from, next) => {
//    const authStore = useAuthStore();

//    if (authStore.isAuthenticated && to.path === appConfig.router.login) {
//       return next(authStore.returnUrl || "/");
//    }

//    if (to.meta.auth !== false && to.path !== appConfig.router.login) {
//       authStore.setUrl(to.fullPath);
//       return next(appConfig.router.login);
//    }

//    next();
// });
