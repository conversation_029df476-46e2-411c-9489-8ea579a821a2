import { AllowedComponentProps, VNodeProps } from "vue";
import { VBtn, VCard, VContainer, VDataTable, VDatePicker, VList, VListItem, VSelect, VTextField, VToolbar } from "vuetify/components";

/* @vue-ignore */
type ComponentProps<C extends Component> = C extends new (...args: any) => any ? Omit<InstanceType<C>["$props"], keyof VNodeProps | keyof AllowedComponentProps> : never;
/* @ts-ignore */
type UnwrapReadonlyArray<A> = A extends Readonly<Array<infer I>> ? I : A;

type BaseTList = Partial<Omit<ComponentProps<typeof VList>, "itemProps">> & {
   itemType?: "subheader" | "divider";
};
type TListWithChildren = BaseTList & {
   itemProps: Partial<ComponentProps<typeof VListItem>> & {
      value: string;
   };
   children: TList[];
};
type TListWithoutChildren = BaseTList & {
   itemProps?: Partial<ComponentProps<typeof VListItem>>;
   children?: undefined;
};
/* @ts-ignore */
export type TList = TListWithChildren | TListWithoutChildren;
/* @ts-ignore */
export type TDataTable = Partial<ComponentProps<typeof VDataTable>>;
/* @ts-ignore */
export type TMultiSelect = Partial<ComponentProps<typeof VSelect>>;
/* @ts-ignore */
export type TDateField = Partial<ComponentProps<typeof VDatePicker>> & Partial<ComponentProps<typeof VTextField>>;
/* @ts-ignore */
export type TToolbar = Partial<ComponentProps<typeof VToolbar>>;
/* @ts-ignore */
export type TCard = Partial<ComponentProps<typeof VCard>>;
/* @ts-ignore */
export type TContainer = Partial<ComponentProps<typeof VContainer>>;
/* @ts-ignore */
export type TBtn = Partial<ComponentProps<typeof VBtn>>;

// import { mdi } from "vuetify/iconsets/mdi-svg";
// import vuetifyColors from "vuetify/util/colors";
// import tailwindColors from "tailwindcss/colors";
import { createVueI18nAdapter } from "vuetify/locale/adapters/vue-i18n";
import * as labsComponents from "vuetify/labs/components";

const colorScheme = {
   light: {
      colors: {
         error: tailwind3.red["600"],
         info: tailwind3.violet["600"],
         warning: tailwind3.amber["600"],
         "on-surface": tailwind3.neutral["800"],
         "on-background": tailwind3.neutral["800"]
      }
   },
   dark: {
      colors: {
         "on-surface": tailwind3.neutral["200"],
         "on-background": tailwind3.neutral["200"]
      }
   }
};

export const vuetify = createVuetify({
   components: {
      ...labsComponents
   },
   locale: {
      adapter: createVueI18nAdapter({ i18n, useI18n })
   },
   theme: {
      defaultTheme: getUserTheme("light"),
      themes: {
         ...colorScheme
      }
   },
   icons: {
      defaultSet: "tabler",
      aliases: {
         // ...aliases,
         ...tablerAliases
         // ...phoAliases
      },
      sets: {
         tabler
      }
   },
   defaults: {
      global: {
         ripple: true
      },
      VTabs: {
         density: "compact"
      },
      VSkeletonLoader: {
         type: "subtitle, divider, text@2, paragraph, subtitle, paragraph"
      },
      VToolbar: {
         flat: true,
         density: "compact"
      },
      VBreadcrumbs: {
         density: "compact"
      },
      VDialog: {
         scrim: "rgb(var(--v-theme-on-surface-bright))",
         persistent: true,
         noClickAnimation: true,

         VCard: {
            elevation: 8
         },
         VToolbar: {
            density: "compact"
         },
         VCardActions: {
            // VBtn: {
            //    density: "default"
            // }
         }
      },
      VIcon: {
         size: "small"
      },
      VBtn: {
         variant: "text",
         density: "comfortable"
      },
      VDatePicker: {
         VBtn: {
            density: "default"
         }
      },
      VTextField: {
         clearable: true,
         density: "compact",
         variant: "outlined"
      },
      VTextarea: {
         clearable: true,
         density: "compact",
         variant: "outlined"
      },
      VMenu: {
         // scrim: "rgb(var(--v-theme-on-surface-bright))"
      },
      VSelect: {
         clearable: true,
         density: "compact",
         variant: "outlined",
         eager: true,
         // menuProps: { scrim: "rgb(var(--v-theme-on-surface-bright))" }

         VList: {
            density: "compact"
         }
      },
      VAutocomplete: {
         clearable: true,
         density: "compact",
         variant: "outlined",
         eager: true
      },
      VSheet: {
         border: true,
         rounded: true
      },
      VChip: {
         size: "small",
         label: true
      },
      VList: {
         lines: false,
         nav: true,
         slim: true,
         density: "compact"
      },
      VCheckbox: {
         color: "primary",

         VIcon: {
            size: "default"
         }
      },
      VCheckboxBtn: {
         color: "primary",
         density: "comfortable"
      },
      VRadioGroup: {
         color: "primary",

         VIcon: {
            size: "default"
         }
      },
      VDataTableFooter: {
         VSelect: {
            clearable: false
         }
      },
      VProgressLinear: {
         color: "primary"
      },
      VCol: {
         cols: 12
      }
   }
});
